{"name": "@synapse/backend", "version": "0.1.0", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "type-check": "tsc --noEmit", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio"}, "dependencies": {"@nestjs/common": "^10.3.0", "@nestjs/core": "^10.3.0", "@nestjs/platform-fastify": "^10.3.0", "@nestjs/websockets": "^10.3.0", "@nestjs/platform-socket.io": "^10.3.0", "@nestjs/passport": "^10.0.3", "@nestjs/jwt": "^10.2.0", "@nestjs/config": "^3.1.1", "@nestjs/bull": "^10.0.1", "@nestjs/swagger": "^7.1.17", "@nestjs/throttler": "^5.1.1", "@synapse/shared": "workspace:*", "@synapse/config": "workspace:*", "bull": "^4.12.2", "bullmq": "^4.15.4", "fastify": "^4.25.2", "ioredis": "^5.3.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.4", "uws": "^22.0.0", "zod": "^3.22.4", "pino": "^8.17.2", "pino-pretty": "^10.3.1"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/node": "^20.10.5", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "jest": "^29.7.0", "prettier": "^3.1.1", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}}