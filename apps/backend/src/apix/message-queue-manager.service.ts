import { Injectable, Logger } from '@nestjs/common';
import { QueueService } from '../queue/queue.service';
import { RedisService } from '../redis/redis.service';
import { PrismaService } from '../database/prisma.service';

@Injectable()
export class MessageQueueManagerService {
  private readonly logger = new Logger(MessageQueueManagerService.name);
  private readonly queues = new Map<string, any>();

  constructor(
    private readonly queueService: QueueService,
    private readonly redis: RedisService,
    private readonly prisma: PrismaService,
  ) {
    this.initializeQueues();
  }

  private async initializeQueues(): Promise<void> {
    const queueNames = [
      'events',
      'notifications', 
      'analytics',
      'billing',
      'agents',
      'tools',
      'workflows',
      'providers',
      'hitl',
      'knowledge',
      'widgets',
      'retries',
      'dead-letter',
    ];

    for (const queueName of queueNames) {
      try {
        const queue = await this.queueService.createQueue(queueName, {
          defaultJobOptions: {
            removeOnComplete: 100,
            removeOnFail: 50,
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
          },
        });

        this.queues.set(queueName, queue);
        this.logger.log(`Queue initialized: ${queueName}`);

        // Set up processors
        await this.setupQueueProcessor(queueName, queue);

      } catch (error) {
        this.logger.error(`Failed to initialize queue ${queueName}:`, error);
      }
    }
  }

  private async setupQueueProcessor(queueName: string, queue: any): Promise<void> {
    queue.process(async (job: any) => {
      const startTime = Date.now();
      
      try {
        await this.processJob(queueName, job.data);
        
        // Track processing metrics
        const processingTime = Date.now() - startTime;
        await this.redis.lpush(
          `metrics:queue:${queueName}:processing_times`,
          processingTime.toString()
        );
        await this.redis.ltrim(`metrics:queue:${queueName}:processing_times`, 0, 999);

        return { success: true, processingTime };

      } catch (error) {
        this.logger.error(`Job processing failed in queue ${queueName}:`, error);
        
        // Track error metrics
        await this.redis.incr(`metrics:queue:${queueName}:errors`);
        
        throw error;
      }
    });

    // Handle failed jobs
    queue.on('failed', async (job: any, error: any) => {
      this.logger.error(`Job failed in queue ${queueName}:`, {
        jobId: job.id,
        data: job.data,
        error: error.message,
      });

      // Move to dead letter queue if max attempts reached
      if (job.attemptsMade >= job.opts.attempts) {
        await this.moveToDeadLetterQueue(queueName, job.data, error.message);
      }
    });

    // Handle completed jobs
    queue.on('completed', async (job: any, result: any) => {
      await this.redis.incr(`metrics:queue:${queueName}:completed`);
    });
  }

  async enqueue(queueName: string, data: {
    type: string;
    payload: any;
    organizationId?: string;
    userId?: string;
    priority?: number;
    delay?: number;
  }): Promise<string> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) {
        throw new Error(`Queue not found: ${queueName}`);
      }

      const jobOptions: any = {};
      
      if (data.priority) {
        jobOptions.priority = data.priority;
      }
      
      if (data.delay) {
        jobOptions.delay = data.delay;
      }

      const job = await queue.add(data.type, data, jobOptions);
      
      // Track enqueue metrics
      await this.redis.incr(`metrics:queue:${queueName}:enqueued`);
      
      this.logger.debug(`Job enqueued: ${queueName}/${data.type} - ${job.id}`);
      return job.id;

    } catch (error) {
      this.logger.error(`Failed to enqueue job in ${queueName}:`, error);
      throw error;
    }
  }

  private async processJob(queueName: string, data: any): Promise<void> {
    switch (queueName) {
      case 'events':
        await this.processEventJob(data);
        break;
      case 'notifications':
        await this.processNotificationJob(data);
        break;
      case 'analytics':
        await this.processAnalyticsJob(data);
        break;
      case 'billing':
        await this.processBillingJob(data);
        break;
      case 'agents':
        await this.processAgentJob(data);
        break;
      case 'tools':
        await this.processToolJob(data);
        break;
      case 'workflows':
        await this.processWorkflowJob(data);
        break;
      case 'providers':
        await this.processProviderJob(data);
        break;
      case 'hitl':
        await this.processHITLJob(data);
        break;
      case 'knowledge':
        await this.processKnowledgeJob(data);
        break;
      case 'widgets':
        await this.processWidgetJob(data);
        break;
      case 'retries':
        await this.processRetryJob(data);
        break;
      default:
        this.logger.warn(`Unknown queue type: ${queueName}`);
    }
  }

  private async processEventJob(data: any): Promise<void> {
    // Process direct message events
    if (data.type === 'direct_message') {
      // This would be handled by the gateway directly
      this.logger.debug('Direct message event processed');
    }
  }

  private async processNotificationJob(data: any): Promise<void> {
    // Process notification events
    if (data.type === 'notification_event') {
      const event = data.payload;
      
      // Create notification delivery record
      await this.prisma.notificationDelivery.create({
        data: {
          templateId: 'system', // TODO: Use actual template
          recipientId: event.userId || 'system',
          recipientType: 'user',
          channel: event.channel,
          payload: event.payload,
          status: 'PENDING',
          organizationId: event.organizationId,
        },
      });
    }
  }

  private async processAnalyticsJob(data: any): Promise<void> {
    // Process analytics events
    if (data.type === 'analytics_event') {
      const event = data.payload;
      
      // Store analytics data
      await this.redis.lpush(
        `analytics:${event.organizationId}:events`,
        JSON.stringify({
          eventType: event.eventType,
          channel: event.channel,
          timestamp: Date.now(),
          metadata: event.metadata,
        })
      );
      
      // Keep only recent events
      await this.redis.ltrim(`analytics:${event.organizationId}:events`, 0, 9999);
    }
  }

  private async processBillingJob(data: any): Promise<void> {
    // Process billing events
    if (data.type === 'billing_event') {
      const event = data.payload;
      
      // Track usage metrics for billing
      await this.redis.incr(`billing:${event.organizationId}:api_calls`);
      await this.redis.incr(`billing:${event.organizationId}:events`);
    }
  }

  private async processAgentJob(data: any): Promise<void> {
    // Process agent events
    if (data.type === 'agent_event') {
      const event = data.payload;
      
      // Update agent metrics
      await this.redis.hset(
        `agent:${event.payload.agentId}:metrics`,
        'last_activity',
        Date.now().toString()
      );
    }
  }

  private async processToolJob(data: any): Promise<void> {
    // Process tool events
    if (data.type === 'tool_event') {
      const event = data.payload;
      
      // Update tool metrics
      await this.redis.hset(
        `tool:${event.payload.toolId}:metrics`,
        'last_activity',
        Date.now().toString()
      );
    }
  }

  private async processWorkflowJob(data: any): Promise<void> {
    // Process workflow events
    if (data.type === 'workflow_event') {
      const event = data.payload;
      
      // Update workflow metrics
      await this.redis.hset(
        `workflow:${event.payload.workflowId}:metrics`,
        'last_activity',
        Date.now().toString()
      );
    }
  }

  private async processProviderJob(data: any): Promise<void> {
    // Process provider events
    if (data.type === 'provider_event') {
      const event = data.payload;
      
      // Update provider health metrics
      await this.redis.hset(
        `provider:${event.payload.providerId}:health`,
        'last_update',
        Date.now().toString()
      );
    }
  }

  private async processHITLJob(data: any): Promise<void> {
    // Process Human-in-the-Loop events
    if (data.type === 'hitl_event') {
      const event = data.payload;
      
      // Queue for human review
      await this.redis.lpush(
        `hitl:${event.organizationId}:pending`,
        JSON.stringify(event)
      );
    }
  }

  private async processKnowledgeJob(data: any): Promise<void> {
    // Process knowledge base events
    if (data.type === 'knowledge_event') {
      const event = data.payload;
      
      // Update knowledge base metrics
      await this.redis.hset(
        `knowledge:${event.organizationId}:metrics`,
        'last_update',
        Date.now().toString()
      );
    }
  }

  private async processWidgetJob(data: any): Promise<void> {
    // Process widget events
    if (data.type === 'widget_event') {
      const event = data.payload;
      
      // Update widget metrics
      await this.redis.hset(
        `widget:${event.payload.widgetId}:metrics`,
        'last_activity',
        Date.now().toString()
      );
    }
  }

  private async processRetryJob(data: any): Promise<void> {
    // Process retry events
    if (data.type === 'failed_delivery') {
      // Attempt to redeliver the failed event
      this.logger.log(`Retrying failed delivery: ${data.payload.eventId}`);
      
      // This would trigger the actual retry logic
      // For now, just log the retry attempt
    }
  }

  private async moveToDeadLetterQueue(queueName: string, jobData: any, error: string): Promise<void> {
    try {
      const deadLetterQueue = this.queues.get('dead-letter');
      if (deadLetterQueue) {
        await deadLetterQueue.add('dead-letter-job', {
          originalQueue: queueName,
          jobData,
          error,
          timestamp: Date.now(),
        });
      }

      // Track dead letter metrics
      await this.redis.incr(`metrics:queue:${queueName}:dead_letter`);

    } catch (error) {
      this.logger.error('Failed to move job to dead letter queue:', error);
    }
  }

  async getQueueStats(queueName: string): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: boolean;
  }> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) {
        throw new Error(`Queue not found: ${queueName}`);
      }

      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed(),
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        paused: await queue.isPaused(),
      };

    } catch (error) {
      this.logger.error(`Failed to get queue stats for ${queueName}:`, error);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        paused: false,
      };
    }
  }

  async getAllQueueStats(): Promise<Record<string, any>> {
    const stats: Record<string, any> = {};
    
    for (const [queueName] of this.queues) {
      stats[queueName] = await this.getQueueStats(queueName);
    }
    
    return stats;
  }

  async pauseQueue(queueName: string): Promise<void> {
    try {
      const queue = this.queues.get(queueName);
      if (queue) {
        await queue.pause();
        this.logger.log(`Queue paused: ${queueName}`);
      }
    } catch (error) {
      this.logger.error(`Failed to pause queue ${queueName}:`, error);
      throw error;
    }
  }

  async resumeQueue(queueName: string): Promise<void> {
    try {
      const queue = this.queues.get(queueName);
      if (queue) {
        await queue.resume();
        this.logger.log(`Queue resumed: ${queueName}`);
      }
    } catch (error) {
      this.logger.error(`Failed to resume queue ${queueName}:`, error);
      throw error;
    }
  }

  async cleanQueue(queueName: string, olderThanMs: number): Promise<number> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) {
        return 0;
      }

      const cleaned = await queue.clean(olderThanMs, 'completed');
      this.logger.log(`Cleaned ${cleaned.length} jobs from queue: ${queueName}`);
      
      return cleaned.length;

    } catch (error) {
      this.logger.error(`Failed to clean queue ${queueName}:`, error);
      return 0;
    }
  }

  async retryFailedJobs(queueName: string, limit: number = 10): Promise<number> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) {
        return 0;
      }

      const failedJobs = await queue.getFailed(0, limit - 1);
      let retriedCount = 0;

      for (const job of failedJobs) {
        try {
          await job.retry();
          retriedCount++;
        } catch (error) {
          this.logger.error(`Failed to retry job ${job.id}:`, error);
        }
      }

      this.logger.log(`Retried ${retriedCount} failed jobs in queue: ${queueName}`);
      return retriedCount;

    } catch (error) {
      this.logger.error(`Failed to retry jobs in queue ${queueName}:`, error);
      return 0;
    }
  }

  async getJobDetails(queueName: string, jobId: string): Promise<any> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) {
        return null;
      }

      const job = await queue.getJob(jobId);
      if (!job) {
        return null;
      }

      return {
        id: job.id,
        name: job.name,
        data: job.data,
        opts: job.opts,
        progress: job.progress(),
        delay: job.delay,
        timestamp: job.timestamp,
        attemptsMade: job.attemptsMade,
        failedReason: job.failedReason,
        stacktrace: job.stacktrace,
        returnvalue: job.returnvalue,
        finishedOn: job.finishedOn,
        processedOn: job.processedOn,
      };

    } catch (error) {
      this.logger.error(`Failed to get job details for ${queueName}/${jobId}:`, error);
      return null;
    }
  }
}