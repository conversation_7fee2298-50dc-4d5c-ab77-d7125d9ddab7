import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis/redis.service';
import { PrismaService } from '../database/prisma.service';

@Injectable()
export class LatencyTrackerService {
  private readonly logger = new Logger(LatencyTrackerService.name);
  private readonly METRICS_RETENTION_HOURS = 24;
  private readonly SAMPLE_RATE = 0.1; // Sample 10% of requests for detailed tracking

  constructor(
    private readonly redis: RedisService,
    private readonly prisma: PrismaService,
  ) {
    // Start periodic aggregation
    this.startPeriodicAggregation();
  }

  async recordLatency(
    connectionId: string,
    eventType: string,
    latency: number,
    channel?: string
  ): Promise<void> {
    try {
      const timestamp = Date.now();
      const minute = Math.floor(timestamp / 60000) * 60000; // Round to minute

      // Store in Redis for real-time metrics
      const key = `latency:${connectionId}:${eventType}`;
      await this.redis.lpush(key, `${timestamp}:${latency}`);
      await this.redis.expire(key, this.METRICS_RETENTION_HOURS * 3600);
      await this.redis.ltrim(key, 0, 999); // Keep last 1000 measurements

      // Aggregate by minute for system-wide metrics
      const minuteKey = `latency:system:${minute}`;
      await this.redis.hincrbyfloat(minuteKey, `${eventType}:sum`, latency);
      await this.redis.hincrby(minuteKey, `${eventType}:count`, 1);
      await this.redis.expire(minuteKey, this.METRICS_RETENTION_HOURS * 3600);

      // Channel-specific metrics
      if (channel) {
        const channelKey = `latency:channel:${channel}:${minute}`;
        await this.redis.hincrbyfloat(channelKey, 'sum', latency);
        await this.redis.hincrby(channelKey, 'count', 1);
        await this.redis.expire(channelKey, this.METRICS_RETENTION_HOURS * 3600);
      }

      // Sample for database storage
      if (Math.random() < this.SAMPLE_RATE) {
        const connection = await this.prisma.apiXConnection.findUnique({
          where: { id: connectionId },
          select: { organizationId: true },
        });

        if (connection) {
          await this.prisma.apiXMetrics.create({
            data: {
              connectionId,
              eventType,
              channel: channel || 'unknown',
              latency,
              throughput: 1,
              errorCount: 0,
              timestamp: new Date(timestamp),
              organizationId: connection.organizationId,
            },
          });
        }
      }

    } catch (error) {
      this.logger.error(`Failed to record latency for ${connectionId}:`, error);
    }
  }

  async recordThroughput(channel: string, count: number): Promise<void> {
    try {
      const timestamp = Date.now();
      const minute = Math.floor(timestamp / 60000) * 60000;

      const key = `throughput:${channel}:${minute}`;
      await this.redis.incrby(key, count);
      await this.redis.expire(key, this.METRICS_RETENTION_HOURS * 3600);

    } catch (error) {
      this.logger.error(`Failed to record throughput for ${channel}:`, error);
    }
  }

  async recordError(connectionId: string, eventType: string, channel?: string): Promise<void> {
    try {
      const timestamp = Date.now();
      const minute = Math.floor(timestamp / 60000) * 60000;

      // Connection-specific error count
      const connectionKey = `errors:${connectionId}:${minute}`;
      await this.redis.hincrby(connectionKey, eventType, 1);
      await this.redis.expire(connectionKey, this.METRICS_RETENTION_HOURS * 3600);

      // System-wide error count
      const systemKey = `errors:system:${minute}`;
      await this.redis.hincrby(systemKey, eventType, 1);
      await this.redis.expire(systemKey, this.METRICS_RETENTION_HOURS * 3600);

      // Channel-specific error count
      if (channel) {
        const channelKey = `errors:channel:${channel}:${minute}`;
        await this.redis.incr(channelKey);
        await this.redis.expire(channelKey, this.METRICS_RETENTION_HOURS * 3600);
      }

    } catch (error) {
      this.logger.error(`Failed to record error for ${connectionId}:`, error);
    }
  }

  async getConnectionLatencyStats(connectionId: string): Promise<{
    averageLatency: number;
    p50Latency: number;
    p95Latency: number;
    p99Latency: number;
    totalRequests: number;
    errorRate: number;
    byEventType: Record<string, {
      averageLatency: number;
      requestCount: number;
    }>;
  }> {
    try {
      const eventTypes = ['connection', 'subscription', 'event', 'ping'];
      const latencies: number[] = [];
      const byEventType: Record<string, { averageLatency: number; requestCount: number }> = {};

      for (const eventType of eventTypes) {
        const key = `latency:${connectionId}:${eventType}`;
        const measurements = await this.redis.lrange(key, 0, -1);
        
        const eventLatencies = measurements.map(m => {
          const [, latency] = m.split(':');
          return parseFloat(latency);
        });

        if (eventLatencies.length > 0) {
          latencies.push(...eventLatencies);
          
          const sum = eventLatencies.reduce((a, b) => a + b, 0);
          byEventType[eventType] = {
            averageLatency: sum / eventLatencies.length,
            requestCount: eventLatencies.length,
          };
        }
      }

      if (latencies.length === 0) {
        return {
          averageLatency: 0,
          p50Latency: 0,
          p95Latency: 0,
          p99Latency: 0,
          totalRequests: 0,
          errorRate: 0,
          byEventType: {},
        };
      }

      latencies.sort((a, b) => a - b);
      
      const averageLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
      const p50Latency = latencies[Math.floor(latencies.length * 0.5)];
      const p95Latency = latencies[Math.floor(latencies.length * 0.95)];
      const p99Latency = latencies[Math.floor(latencies.length * 0.99)];

      // Calculate error rate
      const now = Date.now();
      const hourAgo = now - 3600000;
      let totalErrors = 0;
      
      for (let minute = hourAgo; minute <= now; minute += 60000) {
        const errorKey = `errors:${connectionId}:${Math.floor(minute / 60000) * 60000}`;
        const errors = await this.redis.hgetall(errorKey);
        totalErrors += Object.values(errors).reduce((sum, count) => sum + parseInt(count), 0);
      }

      const errorRate = latencies.length > 0 ? totalErrors / latencies.length : 0;

      return {
        averageLatency,
        p50Latency,
        p95Latency,
        p99Latency,
        totalRequests: latencies.length,
        errorRate,
        byEventType,
      };

    } catch (error) {
      this.logger.error(`Failed to get connection latency stats for ${connectionId}:`, error);
      return {
        averageLatency: 0,
        p50Latency: 0,
        p95Latency: 0,
        p99Latency: 0,
        totalRequests: 0,
        errorRate: 0,
        byEventType: {},
      };
    }
  }

  async getSystemLatencyStats(): Promise<{
    averageLatency: number;
    totalRequests: number;
    requestsPerMinute: number;
    errorRate: number;
    byEventType: Record<string, {
      averageLatency: number;
      requestCount: number;
    }>;
    byChannel: Record<string, {
      averageLatency: number;
      throughput: number;
    }>;
  }> {
    try {
      const now = Date.now();
      const hourAgo = now - 3600000;
      
      let totalLatency = 0;
      let totalRequests = 0;
      let totalErrors = 0;
      const byEventType: Record<string, { averageLatency: number; requestCount: number }> = {};
      const byChannel: Record<string, { averageLatency: number; throughput: number }> = {};

      // Aggregate system metrics for the last hour
      for (let minute = hourAgo; minute <= now; minute += 60000) {
        const minuteTimestamp = Math.floor(minute / 60000) * 60000;
        
        // System latency metrics
        const latencyKey = `latency:system:${minuteTimestamp}`;
        const latencyData = await this.redis.hgetall(latencyKey);
        
        for (const [key, value] of Object.entries(latencyData)) {
          if (key.endsWith(':sum')) {
            const eventType = key.replace(':sum', '');
            const sum = parseFloat(value);
            const count = parseInt(latencyData[`${eventType}:count`] || '0');
            
            if (count > 0) {
              totalLatency += sum;
              totalRequests += count;
              
              if (!byEventType[eventType]) {
                byEventType[eventType] = { averageLatency: 0, requestCount: 0 };
              }
              byEventType[eventType].averageLatency += sum;
              byEventType[eventType].requestCount += count;
            }
          }
        }

        // Error metrics
        const errorKey = `errors:system:${minuteTimestamp}`;
        const errorData = await this.redis.hgetall(errorKey);
        totalErrors += Object.values(errorData).reduce((sum, count) => sum + parseInt(count), 0);
      }

      // Calculate averages for event types
      for (const eventType of Object.keys(byEventType)) {
        if (byEventType[eventType].requestCount > 0) {
          byEventType[eventType].averageLatency /= byEventType[eventType].requestCount;
        }
      }

      // Get channel metrics
      const channels = await this.redis.keys('latency:channel:*');
      for (const channelKey of channels) {
        const channelName = channelKey.split(':')[2];
        const channelData = await this.redis.hgetall(channelKey);
        
        const sum = parseFloat(channelData.sum || '0');
        const count = parseInt(channelData.count || '0');
        
        if (count > 0) {
          byChannel[channelName] = {
            averageLatency: sum / count,
            throughput: count,
          };
        }
      }

      const averageLatency = totalRequests > 0 ? totalLatency / totalRequests : 0;
      const requestsPerMinute = totalRequests / 60; // Average over the hour
      const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;

      return {
        averageLatency,
        totalRequests,
        requestsPerMinute,
        errorRate,
        byEventType,
        byChannel,
      };

    } catch (error) {
      this.logger.error('Failed to get system latency stats:', error);
      return {
        averageLatency: 0,
        totalRequests: 0,
        requestsPerMinute: 0,
        errorRate: 0,
        byEventType: {},
        byChannel: {},
      };
    }
  }

  async getOrganizationLatencyStats(organizationId: string): Promise<{
    averageLatency: number;
    totalRequests: number;
    errorRate: number;
    connectionCount: number;
    topSlowConnections: Array<{
      connectionId: string;
      averageLatency: number;
    }>;
  }> {
    try {
      // Get organization connections
      const connections = await this.prisma.apiXConnection.findMany({
        where: {
          organizationId,
          status: 'CONNECTED',
        },
        select: { id: true },
      });

      let totalLatency = 0;
      let totalRequests = 0;
      let totalErrors = 0;
      const connectionLatencies: Array<{ connectionId: string; averageLatency: number }> = [];

      for (const connection of connections) {
        const stats = await this.getConnectionLatencyStats(connection.id);
        
        if (stats.totalRequests > 0) {
          totalLatency += stats.averageLatency * stats.totalRequests;
          totalRequests += stats.totalRequests;
          totalErrors += stats.errorRate * stats.totalRequests;
          
          connectionLatencies.push({
            connectionId: connection.id,
            averageLatency: stats.averageLatency,
          });
        }
      }

      // Sort connections by latency (highest first)
      connectionLatencies.sort((a, b) => b.averageLatency - a.averageLatency);
      const topSlowConnections = connectionLatencies.slice(0, 10);

      const averageLatency = totalRequests > 0 ? totalLatency / totalRequests : 0;
      const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;

      return {
        averageLatency,
        totalRequests,
        errorRate,
        connectionCount: connections.length,
        topSlowConnections,
      };

    } catch (error) {
      this.logger.error(`Failed to get organization latency stats for ${organizationId}:`, error);
      return {
        averageLatency: 0,
        totalRequests: 0,
        errorRate: 0,
        connectionCount: 0,
        topSlowConnections: [],
      };
    }
  }

  async getChannelLatencyStats(channel: string): Promise<{
    averageLatency: number;
    throughput: number;
    errorRate: number;
    peakThroughput: number;
    latencyTrend: Array<{
      timestamp: number;
      averageLatency: number;
      throughput: number;
    }>;
  }> {
    try {
      const now = Date.now();
      const hourAgo = now - 3600000;
      
      let totalLatency = 0;
      let totalRequests = 0;
      let totalErrors = 0;
      let peakThroughput = 0;
      const latencyTrend: Array<{ timestamp: number; averageLatency: number; throughput: number }> = [];

      for (let minute = hourAgo; minute <= now; minute += 60000) {
        const minuteTimestamp = Math.floor(minute / 60000) * 60000;
        
        // Latency metrics
        const latencyKey = `latency:channel:${channel}:${minuteTimestamp}`;
        const latencyData = await this.redis.hgetall(latencyKey);
        
        const sum = parseFloat(latencyData.sum || '0');
        const count = parseInt(latencyData.count || '0');
        
        if (count > 0) {
          totalLatency += sum;
          totalRequests += count;
          
          const avgLatency = sum / count;
          latencyTrend.push({
            timestamp: minuteTimestamp,
            averageLatency: avgLatency,
            throughput: count,
          });
          
          if (count > peakThroughput) {
            peakThroughput = count;
          }
        }

        // Error metrics
        const errorKey = `errors:channel:${channel}:${minuteTimestamp}`;
        const errorCount = await this.redis.get(errorKey);
        if (errorCount) {
          totalErrors += parseInt(errorCount);
        }
      }

      const averageLatency = totalRequests > 0 ? totalLatency / totalRequests : 0;
      const throughput = totalRequests / 60; // Average per minute
      const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;

      return {
        averageLatency,
        throughput,
        errorRate,
        peakThroughput,
        latencyTrend,
      };

    } catch (error) {
      this.logger.error(`Failed to get channel latency stats for ${channel}:`, error);
      return {
        averageLatency: 0,
        throughput: 0,
        errorRate: 0,
        peakThroughput: 0,
        latencyTrend: [],
      };
    }
  }

  private async startPeriodicAggregation(): Promise<void> {
    const aggregate = async () => {
      try {
        // Aggregate metrics every 5 minutes
        await this.aggregateMetrics();
      } catch (error) {
        this.logger.error('Failed to aggregate metrics:', error);
      }

      // Schedule next aggregation
      setTimeout(aggregate, 5 * 60 * 1000); // 5 minutes
    };

    aggregate();
  }

  private async aggregateMetrics(): Promise<void> {
    try {
      const now = Date.now();
      const fiveMinutesAgo = now - 5 * 60 * 1000;
      
      // Aggregate connection metrics to database
      const connections = await this.prisma.apiXConnection.findMany({
        where: {
          lastHeartbeat: { gte: new Date(fiveMinutesAgo) },
        },
        select: { id: true, organizationId: true },
      });

      for (const connection of connections) {
        const stats = await this.getConnectionLatencyStats(connection.id);
        
        if (stats.totalRequests > 0) {
          // Store aggregated metrics
          await this.prisma.apiXMetrics.create({
            data: {
              connectionId: connection.id,
              eventType: 'aggregated',
              channel: 'system',
              latency: stats.averageLatency,
              throughput: stats.totalRequests,
              errorCount: Math.round(stats.errorRate * stats.totalRequests),
              timestamp: new Date(),
              organizationId: connection.organizationId,
            },
          });
        }
      }

      this.logger.debug(`Aggregated metrics for ${connections.length} connections`);

    } catch (error) {
      this.logger.error('Failed to aggregate metrics:', error);
    }
  }

  async getLatencyAlerts(thresholdMs: number = 5000): Promise<Array<{
    connectionId: string;
    organizationId: string;
    averageLatency: number;
    alertLevel: 'warning' | 'critical';
  }>> {
    try {
      const connections = await this.prisma.apiXConnection.findMany({
        where: { status: 'CONNECTED' },
        select: { id: true, organizationId: true },
      });

      const alerts: Array<{
        connectionId: string;
        organizationId: string;
        averageLatency: number;
        alertLevel: 'warning' | 'critical';
      }> = [];

      for (const connection of connections) {
        const stats = await this.getConnectionLatencyStats(connection.id);
        
        if (stats.averageLatency > thresholdMs) {
          alerts.push({
            connectionId: connection.id,
            organizationId: connection.organizationId!,
            averageLatency: stats.averageLatency,
            alertLevel: stats.averageLatency > thresholdMs * 2 ? 'critical' : 'warning',
          });
        }
      }

      return alerts.sort((a, b) => b.averageLatency - a.averageLatency);

    } catch (error) {
      this.logger.error('Failed to get latency alerts:', error);
      return [];
    }
  }
}