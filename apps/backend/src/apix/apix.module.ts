import { Module } from '@nestjs/common';
import { ApiXGateway } from './apix.gateway';
import { ApiXService } from './apix.service';
import { ApiXController } from './apix.controller';
import { EventRouterService } from './event-router.service';
import { ConnectionManagerService } from './connection-manager.service';
import { SubscriptionManagerService } from './subscription-manager.service';
import { MessageQueueManagerService } from './message-queue-manager.service';
import { RetryManagerService } from './retry-manager.service';
import { LatencyTrackerService } from './latency-tracker.service';
import { AuditLoggerService } from './audit-logger.service';
import { SessionManagerService } from './session-manager.service';
import { DatabaseModule } from '../database/database.module';
import { RedisModule } from '../redis/redis.module';
import { QueueModule } from '../queue/queue.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    DatabaseModule,
    RedisModule,
    QueueModule,
    AuthModule,
  ],
  providers: [
    ApiXGateway,
    ApiXService,
    EventRouterService,
    ConnectionManagerService,
    SubscriptionManagerService,
    MessageQueueManagerService,
    RetryManagerService,
    LatencyTrackerService,
    AuditLoggerService,
    SessionManagerService,
  ],
  controllers: [ApiXController],
  exports: [
    ApiXService,
    EventRouterService,
    ConnectionManagerService,
    SubscriptionManagerService,
    MessageQueueManagerService,
    RetryManagerService,
    LatencyTrackerService,
    AuditLoggerService,
    SessionManagerService,
  ],
})
export class ApiXModule {}