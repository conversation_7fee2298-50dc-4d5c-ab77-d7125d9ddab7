import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { RedisService } from '../redis/redis.service';

@Injectable()
export class AuditLoggerService {
  private readonly logger = new Logger(AuditLoggerService.name);
  private readonly BATCH_SIZE = 100;
  private readonly FLUSH_INTERVAL = 5000; // 5 seconds
  private auditQueue: any[] = [];

  constructor(
    private readonly prisma: PrismaService,
    private readonly redis: RedisService,
  ) {
    // Start periodic flush
    this.startPeriodicFlush();
  }

  async logConnection(data: {
    connectionId: string;
    userId?: string;
    organizationId?: string;
    action: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.queueAuditLog({
      eventType: 'CONNECTION',
      action: data.action,
      entityType: 'ApiXConnection',
      entityId: data.connectionId,
      userId: data.userId,
      organizationId: data.organizationId,
      sessionId: data.connectionId,
      metadata: data.metadata,
    });
  }

  async logSubscription(data: {
    connectionId: string;
    userId?: string;
    organizationId?: string;
    action: string;
    channels: string[];
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.queueAuditLog({
      eventType: 'SUBSCRIPTION',
      action: data.action,
      entityType: 'ApiXSubscription',
      entityId: data.connectionId,
      userId: data.userId,
      organizationId: data.organizationId,
      sessionId: data.connectionId,
      metadata: {
        ...data.metadata,
        channels: data.channels,
      },
    });
  }

  async logEvent(data: {
    eventId: string;
    eventType: string;
    channel: string;
    organizationId?: string;
    userId?: string;
    action: string;
    latency?: number;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.queueAuditLog({
      eventType: 'EVENT',
      action: data.action,
      entityType: 'ApiXEvent',
      entityId: data.eventId,
      userId: data.userId,
      organizationId: data.organizationId,
      metadata: {
        ...data.metadata,
        eventType: data.eventType,
        channel: data.channel,
        latency: data.latency,
      },
    });
  }

  async logSecurity(data: {
    action: string;
    entityType: string;
    entityId: string;
    userId?: string;
    organizationId?: string;
    sessionId?: string;
    ipAddress?: string;
    userAgent?: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.queueAuditLog({
      eventType: 'SECURITY',
      action: data.action,
      entityType: data.entityType,
      entityId: data.entityId,
      userId: data.userId,
      organizationId: data.organizationId,
      sessionId: data.sessionId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      metadata: {
        ...data.metadata,
        severity: data.severity,
      },
    });

    // For high/critical security events, also log to Redis for immediate alerting
    if (data.severity === 'high' || data.severity === 'critical') {
      await this.redis.lpush('security:alerts', JSON.stringify({
        ...data,
        timestamp: Date.now(),
      }));
      await this.redis.ltrim('security:alerts', 0, 999); // Keep last 1000 alerts
    }
  }

  async logPerformance(data: {
    action: string;
    entityType: string;
    entityId: string;
    organizationId?: string;
    userId?: string;
    sessionId?: string;
    metrics: {
      latency?: number;
      throughput?: number;
      errorRate?: number;
      memoryUsage?: number;
      cpuUsage?: number;
    };
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.queueAuditLog({
      eventType: 'PERFORMANCE',
      action: data.action,
      entityType: data.entityType,
      entityId: data.entityId,
      userId: data.userId,
      organizationId: data.organizationId,
      sessionId: data.sessionId,
      metadata: {
        ...data.metadata,
        metrics: data.metrics,
      },
    });
  }

  async logSystemEvent(data: {
    action: string;
    entityType: string;
    entityId: string;
    severity: 'info' | 'warning' | 'error';
    message: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.queueAuditLog({
      eventType: 'SYSTEM',
      action: data.action,
      entityType: data.entityType,
      entityId: data.entityId,
      metadata: {
        ...data.metadata,
        severity: data.severity,
        message: data.message,
      },
    });

    // For errors, also log to Redis for monitoring
    if (data.severity === 'error') {
      await this.redis.lpush('system:errors', JSON.stringify({
        ...data,
        timestamp: Date.now(),
      }));
      await this.redis.ltrim('system:errors', 0, 999);
    }
  }

  async logDataAccess(data: {
    action: string;
    entityType: string;
    entityId: string;
    userId: string;
    organizationId?: string;
    sessionId?: string;
    ipAddress?: string;
    dataType: string;
    dataSize?: number;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.queueAuditLog({
      eventType: 'DATA_ACCESS',
      action: data.action,
      entityType: data.entityType,
      entityId: data.entityId,
      userId: data.userId,
      organizationId: data.organizationId,
      sessionId: data.sessionId,
      ipAddress: data.ipAddress,
      metadata: {
        ...data.metadata,
        dataType: data.dataType,
        dataSize: data.dataSize,
      },
    });
  }

  async logCompliance(data: {
    action: string;
    entityType: string;
    entityId: string;
    userId?: string;
    organizationId?: string;
    complianceType: 'GDPR' | 'HIPAA' | 'SOX' | 'PCI_DSS' | 'SOC2';
    status: 'compliant' | 'violation' | 'warning';
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.queueAuditLog({
      eventType: 'COMPLIANCE',
      action: data.action,
      entityType: data.entityType,
      entityId: data.entityId,
      userId: data.userId,
      organizationId: data.organizationId,
      metadata: {
        ...data.metadata,
        complianceType: data.complianceType,
        status: data.status,
      },
    });

    // For violations, also create immediate alert
    if (data.status === 'violation') {
      await this.redis.lpush('compliance:violations', JSON.stringify({
        ...data,
        timestamp: Date.now(),
      }));
      await this.redis.ltrim('compliance:violations', 0, 999);
    }
  }

  private async queueAuditLog(logData: {
    eventType: string;
    action: string;
    entityType: string;
    entityId: string;
    userId?: string;
    organizationId?: string;
    sessionId?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    try {
      this.auditQueue.push({
        ...logData,
        timestamp: new Date(),
      });

      // Flush immediately if queue is full
      if (this.auditQueue.length >= this.BATCH_SIZE) {
        await this.flushAuditQueue();
      }

    } catch (error) {
      this.logger.error('Failed to queue audit log:', error);
    }
  }

  private async startPeriodicFlush(): Promise<void> {
    const flush = async () => {
      try {
        if (this.auditQueue.length > 0) {
          await this.flushAuditQueue();
        }
      } catch (error) {
        this.logger.error('Failed to flush audit queue:', error);
      }

      // Schedule next flush
      setTimeout(flush, this.FLUSH_INTERVAL);
    };

    flush();
  }

  private async flushAuditQueue(): Promise<void> {
    if (this.auditQueue.length === 0) {
      return;
    }

    const logsToFlush = this.auditQueue.splice(0, this.BATCH_SIZE);

    try {
      await this.prisma.auditLog.createMany({
        data: logsToFlush,
      });

      this.logger.debug(`Flushed ${logsToFlush.length} audit logs to database`);

    } catch (error) {
      this.logger.error('Failed to flush audit logs to database:', error);
      
      // Put logs back in queue for retry
      this.auditQueue.unshift(...logsToFlush);
    }
  }

  async getAuditLogs(filters: {
    organizationId?: string;
    userId?: string;
    eventType?: string;
    action?: string;
    entityType?: string;
    entityId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<{
    logs: any[];
    total: number;
  }> {
    try {
      const where: any = {};

      if (filters.organizationId) where.organizationId = filters.organizationId;
      if (filters.userId) where.userId = filters.userId;
      if (filters.eventType) where.eventType = filters.eventType;
      if (filters.action) where.action = filters.action;
      if (filters.entityType) where.entityType = filters.entityType;
      if (filters.entityId) where.entityId = filters.entityId;
      
      if (filters.startDate || filters.endDate) {
        where.timestamp = {};
        if (filters.startDate) where.timestamp.gte = filters.startDate;
        if (filters.endDate) where.timestamp.lte = filters.endDate;
      }

      const [logs, total] = await Promise.all([
        this.prisma.auditLog.findMany({
          where,
          orderBy: { timestamp: 'desc' },
          take: filters.limit || 50,
          skip: filters.offset || 0,
        }),
        this.prisma.auditLog.count({ where }),
      ]);

      return { logs, total };

    } catch (error) {
      this.logger.error('Failed to get audit logs:', error);
      return { logs: [], total: 0 };
    }
  }

  async getAuditStats(organizationId?: string): Promise<{
    totalLogs: number;
    logsByEventType: Record<string, number>;
    logsByAction: Record<string, number>;
    recentActivity: any[];
    securityAlerts: number;
    complianceViolations: number;
  }> {
    try {
      const where = organizationId ? { organizationId } : {};
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);

      const [
        totalLogs,
        logsByEventType,
        logsByAction,
        recentActivity,
        securityAlerts,
        complianceViolations,
      ] = await Promise.all([
        this.prisma.auditLog.count({ where }),
        this.prisma.auditLog.groupBy({
          by: ['eventType'],
          where,
          _count: { eventType: true },
        }),
        this.prisma.auditLog.groupBy({
          by: ['action'],
          where,
          _count: { action: true },
        }),
        this.prisma.auditLog.findMany({
          where: {
            ...where,
            timestamp: { gte: last24Hours },
          },
          orderBy: { timestamp: 'desc' },
          take: 20,
        }),
        this.redis.llen('security:alerts'),
        this.redis.llen('compliance:violations'),
      ]);

      return {
        totalLogs,
        logsByEventType: logsByEventType.reduce((acc, item) => {
          acc[item.eventType] = item._count.eventType;
          return acc;
        }, {} as Record<string, number>),
        logsByAction: logsByAction.reduce((acc, item) => {
          acc[item.action] = item._count.action;
          return acc;
        }, {} as Record<string, number>),
        recentActivity,
        securityAlerts,
        complianceViolations,
      };

    } catch (error) {
      this.logger.error('Failed to get audit stats:', error);
      return {
        totalLogs: 0,
        logsByEventType: {},
        logsByAction: {},
        recentActivity: [],
        securityAlerts: 0,
        complianceViolations: 0,
      };
    }
  }

  async getSecurityAlerts(limit: number = 50): Promise<any[]> {
    try {
      const alerts = await this.redis.lrange('security:alerts', 0, limit - 1);
      return alerts.map(alert => JSON.parse(alert));
    } catch (error) {
      this.logger.error('Failed to get security alerts:', error);
      return [];
    }
  }

  async getComplianceViolations(limit: number = 50): Promise<any[]> {
    try {
      const violations = await this.redis.lrange('compliance:violations', 0, limit - 1);
      return violations.map(violation => JSON.parse(violation));
    } catch (error) {
      this.logger.error('Failed to get compliance violations:', error);
      return [];
    }
  }

  async getSystemErrors(limit: number = 50): Promise<any[]> {
    try {
      const errors = await this.redis.lrange('system:errors', 0, limit - 1);
      return errors.map(error => JSON.parse(error));
    } catch (error) {
      this.logger.error('Failed to get system errors:', error);
      return [];
    }
  }

  async searchAuditLogs(query: {
    searchTerm: string;
    organizationId?: string;
    eventTypes?: string[];
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }): Promise<any[]> {
    try {
      const where: any = {};

      if (query.organizationId) where.organizationId = query.organizationId;
      if (query.eventTypes?.length) where.eventType = { in: query.eventTypes };
      
      if (query.startDate || query.endDate) {
        where.timestamp = {};
        if (query.startDate) where.timestamp.gte = query.startDate;
        if (query.endDate) where.timestamp.lte = query.endDate;
      }

      // Search in action, entityType, entityId, and metadata
      where.OR = [
        { action: { contains: query.searchTerm, mode: 'insensitive' } },
        { entityType: { contains: query.searchTerm, mode: 'insensitive' } },
        { entityId: { contains: query.searchTerm, mode: 'insensitive' } },
      ];

      const logs = await this.prisma.auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        take: query.limit || 100,
      });

      return logs;

    } catch (error) {
      this.logger.error('Failed to search audit logs:', error);
      return [];
    }
  }

  async exportAuditLogs(filters: {
    organizationId?: string;
    startDate: Date;
    endDate: Date;
    eventTypes?: string[];
    format: 'json' | 'csv';
  }): Promise<string> {
    try {
      const where: any = {
        timestamp: {
          gte: filters.startDate,
          lte: filters.endDate,
        },
      };

      if (filters.organizationId) where.organizationId = filters.organizationId;
      if (filters.eventTypes?.length) where.eventType = { in: filters.eventTypes };

      const logs = await this.prisma.auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
      });

      if (filters.format === 'json') {
        return JSON.stringify(logs, null, 2);
      } else {
        // CSV format
        const headers = [
          'timestamp',
          'eventType',
          'action',
          'entityType',
          'entityId',
          'userId',
          'organizationId',
          'sessionId',
          'ipAddress',
          'userAgent',
        ];

        const csvRows = [
          headers.join(','),
          ...logs.map(log => [
            log.timestamp.toISOString(),
            log.eventType,
            log.action,
            log.entityType,
            log.entityId,
            log.userId || '',
            log.organizationId || '',
            log.sessionId || '',
            log.ipAddress || '',
            log.userAgent || '',
          ].map(field => `"${field}"`).join(',')),
        ];

        return csvRows.join('\n');
      }

    } catch (error) {
      this.logger.error('Failed to export audit logs:', error);
      throw error;
    }
  }

  async cleanupOldLogs(retentionDays: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);

      const result = await this.prisma.auditLog.deleteMany({
        where: {
          timestamp: { lt: cutoffDate },
        },
      });

      this.logger.log(`Cleaned up ${result.count} audit logs older than ${retentionDays} days`);
      return result.count;

    } catch (error) {
      this.logger.error('Failed to cleanup old audit logs:', error);
      return 0;
    }
  }
}