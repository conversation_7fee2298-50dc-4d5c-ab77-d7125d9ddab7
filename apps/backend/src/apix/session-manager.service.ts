import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { RedisService } from '../redis/redis.service';

@Injectable()
export class SessionManagerService {
  private readonly logger = new Logger(SessionManagerService.name);
  private readonly SESSION_TTL = 24 * 60 * 60; // 24 hours in seconds
  private readonly MAX_SESSION_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly MEMORY_CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour

  constructor(
    private readonly prisma: PrismaService,
    private readonly redis: RedisService,
  ) {
    // Start periodic cleanup
    this.startPeriodicCleanup();
  }

  async createSession(data: {
    sessionId: string;
    organizationId: string;
    userId?: string;
    initialData?: Record<string, any>;
    metadata?: Record<string, any>;
  }): Promise<void> {
    try {
      const sessionData = data.initialData || {};
      const sessionSize = JSON.stringify(sessionData).length;

      // Store in database
      await this.prisma.sessionStore.create({
        data: {
          sessionId: data.sessionId,
          organizationId: data.organizationId,
          userId: data.userId,
          data: sessionData,
          metadata: data.metadata,
          size: sessionSize,
          expiresAt: new Date(Date.now() + this.SESSION_TTL * 1000),
        },
      });

      // Store in Redis for fast access
      await this.redis.setex(
        `session:${data.sessionId}`,
        this.SESSION_TTL,
        JSON.stringify({
          organizationId: data.organizationId,
          userId: data.userId,
          data: sessionData,
          metadata: data.metadata,
          size: sessionSize,
          lastAccessed: Date.now(),
        })
      );

      // Track session metrics
      await this.redis.incr('metrics:sessions:created');
      await this.redis.hincr('metrics:sessions:by_org', data.organizationId, 1);

      this.logger.log(`Session created: ${data.sessionId}`);

    } catch (error) {
      this.logger.error(`Failed to create session ${data.sessionId}:`, error);
      throw error;
    }
  }

  async getSession(sessionId: string): Promise<{
    organizationId: string;
    userId?: string;
    data: Record<string, any>;
    metadata?: Record<string, any>;
    size: number;
    lastAccessed: Date;
  } | null> {
    try {
      // Try Redis first
      const cached = await this.redis.get(`session:${sessionId}`);
      if (cached) {
        const session = JSON.parse(cached);
        
        // Update last accessed time
        session.lastAccessed = Date.now();
        await this.redis.setex(`session:${sessionId}`, this.SESSION_TTL, JSON.stringify(session));
        
        return {
          ...session,
          lastAccessed: new Date(session.lastAccessed),
        };
      }

      // Fallback to database
      const session = await this.prisma.sessionStore.findUnique({
        where: { sessionId },
      });

      if (!session) {
        return null;
      }

      // Check if expired
      if (session.expiresAt && session.expiresAt < new Date()) {
        await this.deleteSession(sessionId);
        return null;
      }

      // Cache in Redis
      const sessionData = {
        organizationId: session.organizationId,
        userId: session.userId,
        data: session.data,
        metadata: session.metadata,
        size: session.size,
        lastAccessed: Date.now(),
      };

      await this.redis.setex(`session:${sessionId}`, this.SESSION_TTL, JSON.stringify(sessionData));

      // Update last accessed in database
      await this.prisma.sessionStore.update({
        where: { sessionId },
        data: { lastAccessed: new Date() },
      });

      return {
        ...sessionData,
        lastAccessed: new Date(sessionData.lastAccessed),
      };

    } catch (error) {
      this.logger.error(`Failed to get session ${sessionId}:`, error);
      return null;
    }
  }

  async updateSession(
    sessionId: string,
    updates: {
      data?: Record<string, any>;
      metadata?: Record<string, any>;
    }
  ): Promise<boolean> {
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        return false;
      }

      const updatedData = updates.data ? { ...session.data, ...updates.data } : session.data;
      const updatedMetadata = updates.metadata ? { ...session.metadata, ...updates.metadata } : session.metadata;
      const newSize = JSON.stringify(updatedData).length;

      // Check size limit
      if (newSize > this.MAX_SESSION_SIZE) {
        // Implement smart truncation
        const truncatedData = await this.truncateSessionData(updatedData, this.MAX_SESSION_SIZE);
        const truncatedSize = JSON.stringify(truncatedData).length;

        await this.updateSessionData(sessionId, truncatedData, updatedMetadata, truncatedSize);
        
        this.logger.warn(`Session ${sessionId} truncated from ${newSize} to ${truncatedSize} bytes`);
        return true;
      }

      await this.updateSessionData(sessionId, updatedData, updatedMetadata, newSize);
      return true;

    } catch (error) {
      this.logger.error(`Failed to update session ${sessionId}:`, error);
      return false;
    }
  }

  private async updateSessionData(
    sessionId: string,
    data: Record<string, any>,
    metadata: Record<string, any>,
    size: number
  ): Promise<void> {
    // Update database
    await this.prisma.sessionStore.update({
      where: { sessionId },
      data: {
        data,
        metadata,
        size,
        lastAccessed: new Date(),
      },
    });

    // Update Redis cache
    const sessionData = {
      data,
      metadata,
      size,
      lastAccessed: Date.now(),
    };

    const existing = await this.redis.get(`session:${sessionId}`);
    if (existing) {
      const existingSession = JSON.parse(existing);
      const updatedSession = { ...existingSession, ...sessionData };
      await this.redis.setex(`session:${sessionId}`, this.SESSION_TTL, JSON.stringify(updatedSession));
    }
  }

  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      // Delete from database
      await this.prisma.sessionStore.delete({
        where: { sessionId },
      });

      // Delete from Redis
      await this.redis.del(`session:${sessionId}`);

      // Update metrics
      await this.redis.incr('metrics:sessions:deleted');

      this.logger.log(`Session deleted: ${sessionId}`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to delete session ${sessionId}:`, error);
      return false;
    }
  }

  async getUserSessions(userId: string, organizationId?: string): Promise<string[]> {
    try {
      const where: any = { userId };
      if (organizationId) {
        where.organizationId = organizationId;
      }

      const sessions = await this.prisma.sessionStore.findMany({
        where,
        select: { sessionId: true },
      });

      return sessions.map(s => s.sessionId);

    } catch (error) {
      this.logger.error(`Failed to get user sessions for ${userId}:`, error);
      return [];
    }
  }

  async getOrganizationSessions(organizationId: string): Promise<{
    sessionId: string;
    userId?: string;
    size: number;
    lastAccessed: Date;
  }[]> {
    try {
      const sessions = await this.prisma.sessionStore.findMany({
        where: { organizationId },
        select: {
          sessionId: true,
          userId: true,
          size: true,
          lastAccessed: true,
        },
        orderBy: { lastAccessed: 'desc' },
      });

      return sessions;

    } catch (error) {
      this.logger.error(`Failed to get organization sessions for ${organizationId}:`, error);
      return [];
    }
  }

  async shareSessionData(
    sourceSessionId: string,
    targetSessionId: string,
    keys: string[]
  ): Promise<boolean> {
    try {
      const sourceSession = await this.getSession(sourceSessionId);
      const targetSession = await this.getSession(targetSessionId);

      if (!sourceSession || !targetSession) {
        return false;
      }

      // Only allow sharing within the same organization
      if (sourceSession.organizationId !== targetSession.organizationId) {
        this.logger.warn(`Cross-organization session sharing blocked: ${sourceSessionId} -> ${targetSessionId}`);
        return false;
      }

      const sharedData: Record<string, any> = {};
      for (const key of keys) {
        if (sourceSession.data[key] !== undefined) {
          sharedData[key] = sourceSession.data[key];
        }
      }

      await this.updateSession(targetSessionId, {
        data: sharedData,
        metadata: {
          ...targetSession.metadata,
          sharedFrom: sourceSessionId,
          sharedAt: Date.now(),
          sharedKeys: keys,
        },
      });

      this.logger.log(`Session data shared: ${sourceSessionId} -> ${targetSessionId} (keys: ${keys.join(', ')})`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to share session data: ${sourceSessionId} -> ${targetSessionId}`, error);
      return false;
    }
  }

  async syncSessionAcrossDevices(userId: string, organizationId: string): Promise<void> {
    try {
      const userSessions = await this.getUserSessions(userId, organizationId);
      
      if (userSessions.length <= 1) {
        return; // Nothing to sync
      }

      // Get the most recently accessed session as the source
      const sessions = await Promise.all(
        userSessions.map(async sessionId => ({
          sessionId,
          session: await this.getSession(sessionId),
        }))
      );

      const validSessions = sessions.filter(s => s.session !== null);
      if (validSessions.length <= 1) {
        return;
      }

      validSessions.sort((a, b) => b.session!.lastAccessed.getTime() - a.session!.lastAccessed.getTime());
      const sourceSession = validSessions[0];

      // Sync data to other sessions
      for (let i = 1; i < validSessions.length; i++) {
        const targetSession = validSessions[i];
        
        await this.updateSession(targetSession.sessionId, {
          data: sourceSession.session!.data,
          metadata: {
            ...targetSession.session!.metadata,
            syncedFrom: sourceSession.sessionId,
            syncedAt: Date.now(),
          },
        });
      }

      this.logger.log(`Synced session data for user ${userId} across ${validSessions.length} devices`);

    } catch (error) {
      this.logger.error(`Failed to sync session across devices for user ${userId}:`, error);
    }
  }

  async getSessionStats(organizationId?: string): Promise<{
    totalSessions: number;
    activeSessions: number;
    totalSize: number;
    averageSize: number;
    sessionsByUser: Record<string, number>;
    memoryUsage: {
      total: number;
      byOrganization: Record<string, number>;
    };
  }> {
    try {
      const where = organizationId ? { organizationId } : {};
      const activeThreshold = new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago

      const [
        totalSessions,
        activeSessions,
        sizeStats,
        sessionsByUser,
        memoryUsage,
      ] = await Promise.all([
        this.prisma.sessionStore.count({ where }),
        this.prisma.sessionStore.count({
          where: {
            ...where,
            lastAccessed: { gte: activeThreshold },
          },
        }),
        this.prisma.sessionStore.aggregate({
          where,
          _sum: { size: true },
          _avg: { size: true },
        }),
        this.prisma.sessionStore.groupBy({
          by: ['userId'],
          where: { ...where, userId: { not: null } },
          _count: { userId: true },
        }),
        organizationId ? Promise.resolve([]) : this.prisma.sessionStore.groupBy({
          by: ['organizationId'],
          where: { organizationId: { not: null } },
          _sum: { size: true },
        }),
      ]);

      return {
        totalSessions,
        activeSessions,
        totalSize: sizeStats._sum.size || 0,
        averageSize: sizeStats._avg.size || 0,
        sessionsByUser: sessionsByUser.reduce((acc, item) => {
          if (item.userId) {
            acc[item.userId] = item._count.userId;
          }
          return acc;
        }, {} as Record<string, number>),
        memoryUsage: {
          total: sizeStats._sum.size || 0,
          byOrganization: memoryUsage.reduce((acc, item) => {
            if (item.organizationId) {
              acc[item.organizationId] = item._sum.size || 0;
            }
            return acc;
          }, {} as Record<string, number>),
        },
      };

    } catch (error) {
      this.logger.error('Failed to get session stats:', error);
      return {
        totalSessions: 0,
        activeSessions: 0,
        totalSize: 0,
        averageSize: 0,
        sessionsByUser: {},
        memoryUsage: { total: 0, byOrganization: {} },
      };
    }
  }

  private async truncateSessionData(data: Record<string, any>, maxSize: number): Promise<Record<string, any>> {
    // Smart truncation strategy:
    // 1. Remove oldest entries first
    // 2. Keep essential data
    // 3. Compress large objects

    const essential = ['userId', 'organizationId', 'preferences', 'auth'];
    const truncated: Record<string, any> = {};
    let currentSize = 0;

    // First, preserve essential data
    for (const key of essential) {
      if (data[key] !== undefined) {
        const keySize = JSON.stringify({ [key]: data[key] }).length;
        if (currentSize + keySize <= maxSize * 0.3) { // Reserve 30% for essential data
          truncated[key] = data[key];
          currentSize += keySize;
        }
      }
    }

    // Then add other data by priority (most recent first)
    const otherKeys = Object.keys(data).filter(key => !essential.includes(key));
    const sortedKeys = otherKeys.sort((a, b) => {
      // Prioritize by timestamp if available
      const aTime = data[a]?.timestamp || 0;
      const bTime = data[b]?.timestamp || 0;
      return bTime - aTime;
    });

    for (const key of sortedKeys) {
      const keySize = JSON.stringify({ [key]: data[key] }).length;
      if (currentSize + keySize <= maxSize) {
        truncated[key] = data[key];
        currentSize += keySize;
      } else {
        break;
      }
    }

    // Add truncation metadata
    truncated._truncated = {
      at: Date.now(),
      originalSize: JSON.stringify(data).length,
      truncatedSize: currentSize,
      removedKeys: Object.keys(data).filter(key => !Object.keys(truncated).includes(key)),
    };

    return truncated;
  }

  private async startPeriodicCleanup(): Promise<void> {
    const cleanup = async () => {
      try {
        await this.cleanupExpiredSessions();
        await this.optimizeMemoryUsage();
      } catch (error) {
        this.logger.error('Failed to perform session cleanup:', error);
      }

      // Schedule next cleanup
      setTimeout(cleanup, this.MEMORY_CLEANUP_INTERVAL);
    };

    cleanup();
  }

  private async cleanupExpiredSessions(): Promise<void> {
    try {
      const now = new Date();
      
      const expired = await this.prisma.sessionStore.deleteMany({
        where: {
          expiresAt: { lt: now },
        },
      });

      if (expired.count > 0) {
        this.logger.log(`Cleaned up ${expired.count} expired sessions`);
      }

    } catch (error) {
      this.logger.error('Failed to cleanup expired sessions:', error);
    }
  }

  private async optimizeMemoryUsage(): Promise<void> {
    try {
      // Find sessions that exceed size limits
      const largeSessions = await this.prisma.sessionStore.findMany({
        where: {
          size: { gt: this.MAX_SESSION_SIZE },
        },
        select: { sessionId: true },
      });

      for (const session of largeSessions) {
        const sessionData = await this.getSession(session.sessionId);
        if (sessionData) {
          const truncatedData = await this.truncateSessionData(sessionData.data, this.MAX_SESSION_SIZE);
          await this.updateSession(session.sessionId, { data: truncatedData });
        }
      }

      if (largeSessions.length > 0) {
        this.logger.log(`Optimized ${largeSessions.length} oversized sessions`);
      }

    } catch (error) {
      this.logger.error('Failed to optimize memory usage:', error);
    }
  }

  async extendSessionTTL(sessionId: string, additionalSeconds: number = 3600): Promise<boolean> {
    try {
      const newExpiresAt = new Date(Date.now() + additionalSeconds * 1000);

      await this.prisma.sessionStore.update({
        where: { sessionId },
        data: { expiresAt: newExpiresAt },
      });

      // Update Redis TTL
      await this.redis.expire(`session:${sessionId}`, additionalSeconds);

      return true;

    } catch (error) {
      this.logger.error(`Failed to extend session TTL for ${sessionId}:`, error);
      return false;
    }
  }

  async getSessionAnalytics(organizationId: string, timeRange: {
    start: Date;
    end: Date;
  }): Promise<{
    sessionCount: number;
    uniqueUsers: number;
    averageSessionDuration: number;
    peakConcurrentSessions: number;
    memoryUsagePattern: Array<{
      timestamp: Date;
      totalSize: number;
      sessionCount: number;
    }>;
  }> {
    try {
      const sessions = await this.prisma.sessionStore.findMany({
        where: {
          organizationId,
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end,
          },
        },
        select: {
          sessionId: true,
          userId: true,
          size: true,
          createdAt: true,
          lastAccessed: true,
        },
      });

      const uniqueUsers = new Set(sessions.filter(s => s.userId).map(s => s.userId)).size;
      
      const sessionDurations = sessions.map(s => 
        s.lastAccessed.getTime() - s.createdAt.getTime()
      );
      
      const averageSessionDuration = sessionDurations.length > 0
        ? sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessionDurations.length
        : 0;

      // Calculate memory usage pattern (simplified)
      const memoryUsagePattern = [];
      const hourlyBuckets = Math.ceil((timeRange.end.getTime() - timeRange.start.getTime()) / (60 * 60 * 1000));
      
      for (let i = 0; i < hourlyBuckets; i++) {
        const bucketStart = new Date(timeRange.start.getTime() + i * 60 * 60 * 1000);
        const bucketEnd = new Date(bucketStart.getTime() + 60 * 60 * 1000);
        
        const bucketSessions = sessions.filter(s => 
          s.createdAt >= bucketStart && s.createdAt < bucketEnd
        );
        
        memoryUsagePattern.push({
          timestamp: bucketStart,
          totalSize: bucketSessions.reduce((sum, s) => sum + s.size, 0),
          sessionCount: bucketSessions.length,
        });
      }

      return {
        sessionCount: sessions.length,
        uniqueUsers,
        averageSessionDuration,
        peakConcurrentSessions: Math.max(...memoryUsagePattern.map(p => p.sessionCount)),
        memoryUsagePattern,
      };

    } catch (error) {
      this.logger.error(`Failed to get session analytics for ${organizationId}:`, error);
      return {
        sessionCount: 0,
        uniqueUsers: 0,
        averageSessionDuration: 0,
        peakConcurrentSessions: 0,
        memoryUsagePattern: [],
      };
    }
  }
}