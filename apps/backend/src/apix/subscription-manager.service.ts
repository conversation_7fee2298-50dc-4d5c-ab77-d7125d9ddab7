import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { RedisService } from '../redis/redis.service';
import { AuditLoggerService } from './audit-logger.service';
import { ApiXSubscription, ChannelType } from './apix.types';

@Injectable()
export class SubscriptionManagerService {
  private readonly logger = new Logger(SubscriptionManagerService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly redis: RedisService,
    private readonly auditLogger: AuditLoggerService,
  ) {}

  async addSubscription(subscriptionData: {
    connectionId: string;
    channelName: string;
    filters?: Record<string, any>;
    organizationId: string;
    userId?: string;
  }): Promise<ApiXSubscription> {
    try {
      // Validate channel exists or create it
      await this.ensureChannelExists(subscriptionData.channelName, subscriptionData.organizationId);

      // Create subscription
      const subscription = await this.prisma.apiXSubscription.create({
        data: {
          connectionId: subscriptionData.connectionId,
          channelName: subscriptionData.channelName,
          filters: subscriptionData.filters,
          permissions: {}, // TODO: Implement RBAC permissions
        },
        include: {
          connection: true,
          channel: true,
        },
      });

      // Add to Redis for fast lookups
      await this.redis.sadd(
        `channel:${subscriptionData.channelName}:subscribers`,
        subscriptionData.connectionId
      );

      await this.redis.sadd(
        `connection:${subscriptionData.connectionId}:subscriptions`,
        subscriptionData.channelName
      );

      // Update channel subscriber count
      await this.prisma.apiXChannel.update({
        where: { name: subscriptionData.channelName },
        data: { subscribers: { increment: 1 } },
      });

      // Audit log
      await this.auditLogger.logSubscription({
        connectionId: subscriptionData.connectionId,
        userId: subscriptionData.userId,
        organizationId: subscriptionData.organizationId,
        action: 'SUBSCRIBED',
        channels: [subscriptionData.channelName],
        metadata: { filters: subscriptionData.filters },
      });

      this.logger.log(`Subscription added: ${subscriptionData.connectionId} -> ${subscriptionData.channelName}`);
      return subscription;

    } catch (error) {
      this.logger.error(`Failed to add subscription:`, error);
      throw error;
    }
  }

  async removeSubscription(connectionId: string, channelName: string): Promise<void> {
    try {
      // Remove from database
      const deleted = await this.prisma.apiXSubscription.deleteMany({
        where: {
          connectionId,
          channelName,
        },
      });

      if (deleted.count > 0) {
        // Remove from Redis
        await this.redis.srem(
          `channel:${channelName}:subscribers`,
          connectionId
        );

        await this.redis.srem(
          `connection:${connectionId}:subscriptions`,
          channelName
        );

        // Update channel subscriber count
        await this.prisma.apiXChannel.update({
          where: { name: channelName },
          data: { subscribers: { decrement: 1 } },
        });

        this.logger.log(`Subscription removed: ${connectionId} -> ${channelName}`);
      }

    } catch (error) {
      this.logger.error(`Failed to remove subscription:`, error);
      throw error;
    }
  }

  async removeAllSubscriptions(connectionId: string): Promise<void> {
    try {
      // Get all subscriptions for this connection
      const subscriptions = await this.prisma.apiXSubscription.findMany({
        where: { connectionId },
        select: { channelName: true },
      });

      // Remove from database
      await this.prisma.apiXSubscription.deleteMany({
        where: { connectionId },
      });

      // Remove from Redis and update channel counts
      for (const subscription of subscriptions) {
        await this.redis.srem(
          `channel:${subscription.channelName}:subscribers`,
          connectionId
        );

        await this.prisma.apiXChannel.update({
          where: { name: subscription.channelName },
          data: { subscribers: { decrement: 1 } },
        });
      }

      // Remove connection subscriptions set
      await this.redis.del(`connection:${connectionId}:subscriptions`);

      this.logger.log(`All subscriptions removed for connection: ${connectionId}`);

    } catch (error) {
      this.logger.error(`Failed to remove all subscriptions for ${connectionId}:`, error);
      throw error;
    }
  }

  async getChannelSubscribers(channelName: string, organizationId?: string): Promise<{
    connectionId: string;
    filters?: Record<string, any>;
    permissions?: Record<string, any>;
  }[]> {
    try {
      const where: any = { channelName };
      
      if (organizationId) {
        where.connection = {
          organizationId,
        };
      }

      const subscriptions = await this.prisma.apiXSubscription.findMany({
        where,
        select: {
          connectionId: true,
          filters: true,
          permissions: true,
        },
      });

      return subscriptions;

    } catch (error) {
      this.logger.error(`Failed to get channel subscribers for ${channelName}:`, error);
      return [];
    }
  }

  async getConnectionSubscriptions(connectionId: string): Promise<{
    channelName: string;
    filters?: Record<string, any>;
    permissions?: Record<string, any>;
    channel: {
      type: string;
      isActive: boolean;
    };
  }[]> {
    try {
      const subscriptions = await this.prisma.apiXSubscription.findMany({
        where: { connectionId },
        select: {
          channelName: true,
          filters: true,
          permissions: true,
          channel: {
            select: {
              type: true,
              isActive: true,
            },
          },
        },
      });

      return subscriptions;

    } catch (error) {
      this.logger.error(`Failed to get connection subscriptions for ${connectionId}:`, error);
      return [];
    }
  }

  async updateSubscriptionFilters(
    connectionId: string,
    channelName: string,
    filters: Record<string, any>
  ): Promise<void> {
    try {
      await this.prisma.apiXSubscription.updateMany({
        where: {
          connectionId,
          channelName,
        },
        data: { filters },
      });

      this.logger.log(`Subscription filters updated: ${connectionId} -> ${channelName}`);

    } catch (error) {
      this.logger.error(`Failed to update subscription filters:`, error);
      throw error;
    }
  }

  async validateSubscriptionPermissions(
    connectionId: string,
    channelName: string,
    action: string
  ): Promise<boolean> {
    try {
      const subscription = await this.prisma.apiXSubscription.findFirst({
        where: {
          connectionId,
          channelName,
        },
        include: {
          connection: true,
          channel: true,
        },
      });

      if (!subscription) {
        return false;
      }

      // Check if channel is active
      if (!subscription.channel.isActive) {
        return false;
      }

      // TODO: Implement RBAC permission checking
      // For now, allow all actions for subscribed connections
      return true;

    } catch (error) {
      this.logger.error(`Failed to validate subscription permissions:`, error);
      return false;
    }
  }

  async getSubscriptionStats(organizationId?: string): Promise<{
    totalSubscriptions: number;
    activeChannels: number;
    subscriptionsByChannel: Record<string, number>;
    subscriptionsByType: Record<string, number>;
    averageSubscriptionsPerConnection: number;
  }> {
    try {
      const where = organizationId ? {
        connection: { organizationId },
      } : {};

      const [
        totalSubscriptions,
        subscriptionsByChannel,
        activeChannels,
        subscriptionsByType,
        connectionCounts,
      ] = await Promise.all([
        this.prisma.apiXSubscription.count({ where }),
        this.prisma.apiXSubscription.groupBy({
          by: ['channelName'],
          where,
          _count: { channelName: true },
        }),
        this.prisma.apiXChannel.count({
          where: {
            isActive: true,
            ...(organizationId && { organizationId }),
          },
        }),
        this.prisma.apiXSubscription.groupBy({
          by: ['channel', 'type'],
          where,
          _count: { channelName: true },
        }),
        this.prisma.apiXSubscription.groupBy({
          by: ['connectionId'],
          where,
          _count: { connectionId: true },
        }),
      ]);

      const averageSubscriptionsPerConnection = connectionCounts.length > 0
        ? connectionCounts.reduce((sum, item) => sum + item._count.connectionId, 0) / connectionCounts.length
        : 0;

      return {
        totalSubscriptions,
        activeChannels,
        subscriptionsByChannel: subscriptionsByChannel.reduce((acc, item) => {
          acc[item.channelName] = item._count.channelName;
          return acc;
        }, {} as Record<string, number>),
        subscriptionsByType: subscriptionsByType.reduce((acc, item) => {
          acc[item.channel.type] = (acc[item.channel.type] || 0) + item._count.channelName;
          return acc;
        }, {} as Record<string, number>),
        averageSubscriptionsPerConnection,
      };

    } catch (error) {
      this.logger.error('Failed to get subscription stats:', error);
      return {
        totalSubscriptions: 0,
        activeChannels: 0,
        subscriptionsByChannel: {},
        subscriptionsByType: {},
        averageSubscriptionsPerConnection: 0,
      };
    }
  }

  async cleanupStaleSubscriptions(): Promise<void> {
    try {
      // Find subscriptions for disconnected connections
      const staleSubscriptions = await this.prisma.apiXSubscription.findMany({
        where: {
          connection: {
            status: 'DISCONNECTED',
            updatedAt: {
              lt: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
            },
          },
        },
        select: {
          connectionId: true,
          channelName: true,
        },
      });

      // Remove stale subscriptions
      for (const subscription of staleSubscriptions) {
        await this.removeSubscription(subscription.connectionId, subscription.channelName);
      }

      if (staleSubscriptions.length > 0) {
        this.logger.log(`Cleaned up ${staleSubscriptions.length} stale subscriptions`);
      }

    } catch (error) {
      this.logger.error('Failed to cleanup stale subscriptions:', error);
    }
  }

  private async ensureChannelExists(channelName: string, organizationId: string): Promise<void> {
    try {
      const existingChannel = await this.prisma.apiXChannel.findUnique({
        where: { name: channelName },
      });

      if (!existingChannel) {
        // Determine channel type based on name
        let channelType = ChannelType.SYSTEM_EVENTS;
        
        if (channelName.startsWith('agent')) {
          channelType = ChannelType.AGENT_EVENTS;
        } else if (channelName.startsWith('tool')) {
          channelType = ChannelType.TOOL_EVENTS;
        } else if (channelName.startsWith('workflow')) {
          channelType = ChannelType.WORKFLOW_EVENTS;
        } else if (channelName.startsWith('provider')) {
          channelType = ChannelType.PROVIDER_EVENTS;
        } else if (channelName.startsWith('user:')) {
          channelType = ChannelType.PRIVATE_USER;
        } else if (channelName.startsWith('org:')) {
          channelType = ChannelType.ORGANIZATION;
        }

        await this.prisma.apiXChannel.create({
          data: {
            name: channelName,
            type: channelType,
            organizationId,
            permissions: {}, // TODO: Set default permissions
            subscribers: 0,
            isActive: true,
            metadata: {
              createdBy: 'system',
              autoCreated: true,
            },
          },
        });

        this.logger.log(`Channel created: ${channelName} (${channelType})`);
      }

    } catch (error) {
      this.logger.error(`Failed to ensure channel exists: ${channelName}`, error);
      throw error;
    }
  }

  async createChannel(channelData: {
    name: string;
    type: ChannelType;
    organizationId?: string;
    permissions: Record<string, any>;
    metadata?: Record<string, any>;
  }): Promise<void> {
    try {
      await this.prisma.apiXChannel.create({
        data: {
          name: channelData.name,
          type: channelData.type,
          organizationId: channelData.organizationId,
          permissions: channelData.permissions,
          subscribers: 0,
          isActive: true,
          metadata: channelData.metadata,
        },
      });

      this.logger.log(`Channel created: ${channelData.name} (${channelData.type})`);

    } catch (error) {
      this.logger.error(`Failed to create channel: ${channelData.name}`, error);
      throw error;
    }
  }

  async deactivateChannel(channelName: string): Promise<void> {
    try {
      await this.prisma.apiXChannel.update({
        where: { name: channelName },
        data: { isActive: false },
      });

      // Remove all subscriptions to this channel
      const subscriptions = await this.prisma.apiXSubscription.findMany({
        where: { channelName },
        select: { connectionId: true },
      });

      for (const subscription of subscriptions) {
        await this.removeSubscription(subscription.connectionId, channelName);
      }

      this.logger.log(`Channel deactivated: ${channelName}`);

    } catch (error) {
      this.logger.error(`Failed to deactivate channel: ${channelName}`, error);
      throw error;
    }
  }

  async getChannelInfo(channelName: string): Promise<{
    channel: any;
    subscribers: number;
    recentActivity: any[];
  }> {
    try {
      const [channel, recentEvents] = await Promise.all([
        this.prisma.apiXChannel.findUnique({
          where: { name: channelName },
        }),
        this.prisma.apiXEvent.findMany({
          where: { channel: channelName },
          orderBy: { createdAt: 'desc' },
          take: 10,
        }),
      ]);

      return {
        channel,
        subscribers: channel?.subscribers || 0,
        recentActivity: recentEvents,
      };

    } catch (error) {
      this.logger.error(`Failed to get channel info for ${channelName}:`, error);
      return {
        channel: null,
        subscribers: 0,
        recentActivity: [],
      };
    }
  }
}