import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiXService } from './apix.service';
import { EventRouterService } from './event-router.service';
import { ConnectionManagerService } from './connection-manager.service';
import { SubscriptionManagerService } from './subscription-manager.service';
import { MessageQueueManagerService } from './message-queue-manager.service';
import { RetryManagerService } from './retry-manager.service';
import { LatencyTrackerService } from './latency-tracker.service';
import { AuditLoggerService } from './audit-logger.service';
import { SessionManagerService } from './session-manager.service';
import {
  ApiXEventSchema,
  ApiXSubscriptionSchema,
  ClientType,
  ChannelType,
} from './apix.types';

@Controller('apix')
@UseGuards(JwtAuthGuard)
export class ApiXController {
  constructor(
    private readonly apiXService: ApiXService,
    private readonly eventRouter: EventRouterService,
    private readonly connectionManager: ConnectionManagerService,
    private readonly subscriptionManager: SubscriptionManagerService,
    private readonly messageQueueManager: MessageQueueManagerService,
    private readonly retryManager: RetryManagerService,
    private readonly latencyTracker: LatencyTrackerService,
    private readonly auditLogger: AuditLoggerService,
    private readonly sessionManager: SessionManagerService,
  ) {}

  // Event Management
  @Post('events')
  async broadcastEvent(@Body() eventData: any) {
    try {
      const validatedEvent = ApiXEventSchema.parse(eventData);
      await this.apiXService.broadcastEvent(validatedEvent);
      return { success: true, message: 'Event broadcasted successfully' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to broadcast event', details: error.message },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post('events/send-to-connection/:connectionId')
  async sendToConnection(
    @Param('connectionId') connectionId: string,
    @Body() eventData: any
  ) {
    try {
      const success = await this.apiXService.sendToConnection(connectionId, eventData);
      return { success, message: success ? 'Event sent successfully' : 'Failed to send event' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to send event to connection', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('events/send-to-user/:userId')
  async sendToUser(
    @Param('userId') userId: string,
    @Body() eventData: any,
    @Query('organizationId') organizationId?: string
  ) {
    try {
      const sentCount = await this.apiXService.sendToUser(userId, eventData, organizationId);
      return { success: true, sentCount, message: `Event sent to ${sentCount} connections` };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to send event to user', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('events/send-to-organization/:organizationId')
  async sendToOrganization(
    @Param('organizationId') organizationId: string,
    @Body() eventData: any
  ) {
    try {
      const sentCount = await this.apiXService.sendToOrganization(organizationId, eventData);
      return { success: true, sentCount, message: `Event sent to ${sentCount} connections` };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to send event to organization', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Connection Management
  @Get('connections')
  async getConnections(@Query('organizationId') organizationId?: string) {
    try {
      const stats = await this.connectionManager.getConnectionStats(organizationId);
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get connections', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('connections/:connectionId')
  async getConnection(@Param('connectionId') connectionId: string) {
    try {
      const connection = await this.connectionManager.getConnection(connectionId);
      if (!connection) {
        throw new HttpException('Connection not found', HttpStatus.NOT_FOUND);
      }
      return { success: true, data: connection };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get connection', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('connections/:connectionId/lifecycle')
  async getConnectionLifecycle(@Param('connectionId') connectionId: string) {
    try {
      const lifecycle = await this.connectionManager.getConnectionLifecycle(connectionId);
      return { success: true, data: lifecycle };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get connection lifecycle', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('connections/:connectionId/suspend')
  async suspendConnection(
    @Param('connectionId') connectionId: string,
    @Body() body: { reason: string }
  ) {
    try {
      await this.connectionManager.suspendConnection(connectionId, body.reason);
      return { success: true, message: 'Connection suspended successfully' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to suspend connection', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('connections/:connectionId/resume')
  async resumeConnection(@Param('connectionId') connectionId: string) {
    try {
      await this.connectionManager.resumeConnection(connectionId);
      return { success: true, message: 'Connection resumed successfully' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to resume connection', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Subscription Management
  @Get('subscriptions')
  async getSubscriptions(@Query('organizationId') organizationId?: string) {
    try {
      const stats = await this.subscriptionManager.getSubscriptionStats(organizationId);
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get subscriptions', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('subscriptions/connection/:connectionId')
  async getConnectionSubscriptions(@Param('connectionId') connectionId: string) {
    try {
      const subscriptions = await this.subscriptionManager.getConnectionSubscriptions(connectionId);
      return { success: true, data: subscriptions };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get connection subscriptions', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('subscriptions/channel/:channelName')
  async getChannelSubscribers(
    @Param('channelName') channelName: string,
    @Query('organizationId') organizationId?: string
  ) {
    try {
      const subscribers = await this.subscriptionManager.getChannelSubscribers(channelName, organizationId);
      return { success: true, data: subscribers };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get channel subscribers', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('channels')
  async createChannel(@Body() channelData: {
    name: string;
    type: ChannelType;
    organizationId?: string;
    permissions: Record<string, any>;
    metadata?: Record<string, any>;
  }) {
    try {
      await this.subscriptionManager.createChannel(channelData);
      return { success: true, message: 'Channel created successfully' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to create channel', details: error.message },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Delete('channels/:channelName')
  async deactivateChannel(@Param('channelName') channelName: string) {
    try {
      await this.subscriptionManager.deactivateChannel(channelName);
      return { success: true, message: 'Channel deactivated successfully' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to deactivate channel', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('channels/:channelName/info')
  async getChannelInfo(@Param('channelName') channelName: string) {
    try {
      const info = await this.subscriptionManager.getChannelInfo(channelName);
      return { success: true, data: info };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get channel info', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Queue Management
  @Get('queues')
  async getQueueStats() {
    try {
      const stats = await this.messageQueueManager.getAllQueueStats();
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get queue stats', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('queues/:queueName')
  async getQueueInfo(@Param('queueName') queueName: string) {
    try {
      const stats = await this.messageQueueManager.getQueueStats(queueName);
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get queue info', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('queues/:queueName/pause')
  async pauseQueue(@Param('queueName') queueName: string) {
    try {
      await this.messageQueueManager.pauseQueue(queueName);
      return { success: true, message: 'Queue paused successfully' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to pause queue', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('queues/:queueName/resume')
  async resumeQueue(@Param('queueName') queueName: string) {
    try {
      await this.messageQueueManager.resumeQueue(queueName);
      return { success: true, message: 'Queue resumed successfully' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to resume queue', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('queues/:queueName/clean')
  async cleanQueue(
    @Param('queueName') queueName: string,
    @Body() body: { olderThanMs: number }
  ) {
    try {
      const cleaned = await this.messageQueueManager.cleanQueue(queueName, body.olderThanMs);
      return { success: true, cleaned, message: `Cleaned ${cleaned} jobs from queue` };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to clean queue', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('queues/:queueName/retry-failed')
  async retryFailedJobs(
    @Param('queueName') queueName: string,
    @Query('limit') limit?: number
  ) {
    try {
      const retried = await this.messageQueueManager.retryFailedJobs(queueName, limit);
      return { success: true, retried, message: `Retried ${retried} failed jobs` };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to retry failed jobs', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Retry Management
  @Get('retries')
  async getRetryStats(@Query('organizationId') organizationId?: string) {
    try {
      const stats = await this.retryManager.getRetryStats(organizationId);
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get retry stats', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('retries/dead-letter')
  async getDeadLetterItems(@Query('limit') limit?: number) {
    try {
      const items = await this.retryManager.getDeadLetterItems(limit);
      return { success: true, data: items };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get dead letter items', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('retries/dead-letter/:index/reprocess')
  async reprocessDeadLetterItem(@Param('index') index: number) {
    try {
      const success = await this.retryManager.reprocessDeadLetterItem(index);
      return { success, message: success ? 'Item reprocessed successfully' : 'Failed to reprocess item' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to reprocess dead letter item', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete('retries/dead-letter')
  async clearDeadLetterQueue() {
    try {
      const cleared = await this.retryManager.clearDeadLetterQueue();
      return { success: true, cleared, message: `Cleared ${cleared} items from dead letter queue` };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to clear dead letter queue', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('retries/events/:eventId/history')
  async getRetryHistory(@Param('eventId') eventId: string) {
    try {
      const history = await this.retryManager.getRetryHistory(eventId);
      return { success: true, data: history };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get retry history', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete('retries/events/:eventId')
  async cancelRetry(@Param('eventId') eventId: string) {
    try {
      const success = await this.retryManager.cancelRetry(eventId);
      return { success, message: success ? 'Retry cancelled successfully' : 'Retry not found' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to cancel retry', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Latency & Performance
  @Get('latency/system')
  async getSystemLatencyStats() {
    try {
      const stats = await this.latencyTracker.getSystemLatencyStats();
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get system latency stats', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('latency/connection/:connectionId')
  async getConnectionLatencyStats(@Param('connectionId') connectionId: string) {
    try {
      const stats = await this.latencyTracker.getConnectionLatencyStats(connectionId);
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get connection latency stats', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('latency/organization/:organizationId')
  async getOrganizationLatencyStats(@Param('organizationId') organizationId: string) {
    try {
      const stats = await this.latencyTracker.getOrganizationLatencyStats(organizationId);
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get organization latency stats', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('latency/channel/:channelName')
  async getChannelLatencyStats(@Param('channelName') channelName: string) {
    try {
      const stats = await this.latencyTracker.getChannelLatencyStats(channelName);
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get channel latency stats', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('latency/alerts')
  async getLatencyAlerts(@Query('threshold') threshold?: number) {
    try {
      const alerts = await this.latencyTracker.getLatencyAlerts(threshold);
      return { success: true, data: alerts };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get latency alerts', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Session Management
  @Get('sessions')
  async getSessionStats(@Query('organizationId') organizationId?: string) {
    try {
      const stats = await this.sessionManager.getSessionStats(organizationId);
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get session stats', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('sessions/:sessionId')
  async getSession(@Param('sessionId') sessionId: string) {
    try {
      const session = await this.sessionManager.getSession(sessionId);
      if (!session) {
        throw new HttpException('Session not found', HttpStatus.NOT_FOUND);
      }
      return { success: true, data: session };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get session', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('sessions/:sessionId')
  async updateSession(
    @Param('sessionId') sessionId: string,
    @Body() updates: { data?: Record<string, any>; metadata?: Record<string, any> }
  ) {
    try {
      const success = await this.sessionManager.updateSession(sessionId, updates);
      return { success, message: success ? 'Session updated successfully' : 'Session not found' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to update session', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete('sessions/:sessionId')
  async deleteSession(@Param('sessionId') sessionId: string) {
    try {
      const success = await this.sessionManager.deleteSession(sessionId);
      return { success, message: success ? 'Session deleted successfully' : 'Session not found' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to delete session', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('sessions/user/:userId')
  async getUserSessions(
    @Param('userId') userId: string,
    @Query('organizationId') organizationId?: string
  ) {
    try {
      const sessions = await this.sessionManager.getUserSessions(userId, organizationId);
      return { success: true, data: sessions };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get user sessions', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('sessions/sync/:userId')
  async syncUserSessions(
    @Param('userId') userId: string,
    @Body() body: { organizationId: string }
  ) {
    try {
      await this.sessionManager.syncSessionAcrossDevices(userId, body.organizationId);
      return { success: true, message: 'Sessions synced successfully' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to sync sessions', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Audit & Compliance
  @Get('audit/logs')
  async getAuditLogs(@Query() filters: {
    organizationId?: string;
    userId?: string;
    eventType?: string;
    action?: string;
    entityType?: string;
    entityId?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
    offset?: number;
  }) {
    try {
      const parsedFilters = {
        ...filters,
        startDate: filters.startDate ? new Date(filters.startDate) : undefined,
        endDate: filters.endDate ? new Date(filters.endDate) : undefined,
        limit: filters.limit ? parseInt(filters.limit.toString()) : undefined,
        offset: filters.offset ? parseInt(filters.offset.toString()) : undefined,
      };

      const result = await this.auditLogger.getAuditLogs(parsedFilters);
      return { success: true, data: result };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get audit logs', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('audit/stats')
  async getAuditStats(@Query('organizationId') organizationId?: string) {
    try {
      const stats = await this.auditLogger.getAuditStats(organizationId);
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get audit stats', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('audit/security-alerts')
  async getSecurityAlerts(@Query('limit') limit?: number) {
    try {
      const alerts = await this.auditLogger.getSecurityAlerts(limit);
      return { success: true, data: alerts };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get security alerts', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('audit/compliance-violations')
  async getComplianceViolations(@Query('limit') limit?: number) {
    try {
      const violations = await this.auditLogger.getComplianceViolations(limit);
      return { success: true, data: violations };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get compliance violations', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('audit/search')
  async searchAuditLogs(@Query() query: {
    searchTerm: string;
    organizationId?: string;
    eventTypes?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
  }) {
    try {
      const searchQuery = {
        ...query,
        eventTypes: query.eventTypes ? query.eventTypes.split(',') : undefined,
        startDate: query.startDate ? new Date(query.startDate) : undefined,
        endDate: query.endDate ? new Date(query.endDate) : undefined,
        limit: query.limit ? parseInt(query.limit.toString()) : undefined,
      };

      const results = await this.auditLogger.searchAuditLogs(searchQuery);
      return { success: true, data: results };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to search audit logs', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // System Health & Stats
  @Get('health')
  async getHealthCheck() {
    try {
      const health = await this.apiXService.healthCheck();
      return health;
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get health status', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('stats')
  async getSystemStats() {
    try {
      const stats = await this.apiXService.getSystemStats();
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get system stats', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('stats/organization/:organizationId')
  async getOrganizationStats(@Param('organizationId') organizationId: string) {
    try {
      const stats = await this.apiXService.getOrganizationStats(organizationId);
      return { success: true, data: stats };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to get organization stats', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('maintenance')
  async performMaintenance() {
    try {
      await this.apiXService.performMaintenance();
      return { success: true, message: 'Maintenance completed successfully' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to perform maintenance', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Event Emission Helpers
  @Post('emit/agent-status')
  async emitAgentStatusUpdate(@Body() body: {
    agentId: string;
    status: string;
    organizationId: string;
  }) {
    try {
      await this.apiXService.emitAgentStatusUpdate(body.agentId, body.status, body.organizationId);
      return { success: true, message: 'Agent status update emitted' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to emit agent status update', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('emit/tool-call-start')
  async emitToolCallStart(@Body() body: {
    toolId: string;
    input: any;
    organizationId: string;
    userId?: string;
  }) {
    try {
      await this.apiXService.emitToolCallStart(body.toolId, body.input, body.organizationId, body.userId);
      return { success: true, message: 'Tool call start emitted' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to emit tool call start', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('emit/workflow-state-change')
  async emitWorkflowStateChange(@Body() body: {
    workflowId: string;
    state: string;
    organizationId: string;
  }) {
    try {
      await this.apiXService.emitWorkflowStateChange(body.workflowId, body.state, body.organizationId);
      return { success: true, message: 'Workflow state change emitted' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to emit workflow state change', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('emit/system-notification')
  async emitSystemNotification(@Body() body: {
    message: string;
    level: 'info' | 'warning' | 'error';
    organizationId?: string;
  }) {
    try {
      await this.apiXService.emitSystemNotification(body.message, body.level, body.organizationId);
      return { success: true, message: 'System notification emitted' };
    } catch (error) {
      throw new HttpException(
        { error: 'Failed to emit system notification', details: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}