import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { RedisService } from '../redis/redis.service';
import { AuditLoggerService } from './audit-logger.service';
import { ApiXConnection, ConnectionStatus, ClientType } from './apix.types';

@Injectable()
export class ConnectionManagerService {
  private readonly logger = new Logger(ConnectionManagerService.name);
  private readonly HEARTBEAT_TIMEOUT = 60000; // 60 seconds
  private readonly CONNECTION_CLEANUP_INTERVAL = 300000; // 5 minutes

  constructor(
    private readonly prisma: PrismaService,
    private readonly redis: RedisService,
    private readonly auditLogger: AuditLoggerService,
  ) {
    // Start periodic cleanup
    setInterval(() => {
      this.performCleanup();
    }, this.CONNECTION_CLEANUP_INTERVAL);
  }

  async registerConnection(connectionData: ApiXConnection): Promise<ApiXConnection> {
    try {
      // Store in database
      const connection = await this.prisma.apiXConnection.create({
        data: {
          id: connectionData.id,
          sessionId: connectionData.sessionId,
          organizationId: connectionData.organizationId,
          userId: connectionData.userId,
          clientType: connectionData.clientType,
          channels: connectionData.channels,
          metadata: connectionData.metadata,
          connectedAt: connectionData.connectedAt,
          lastHeartbeat: connectionData.lastHeartbeat,
          status: connectionData.status,
          ipAddress: connectionData.ipAddress,
          userAgent: connectionData.userAgent,
        },
      });

      // Store in Redis for fast access
      await this.redis.setex(
        `connection:${connectionData.id}`,
        3600, // 1 hour TTL
        JSON.stringify(connection)
      );

      // Add to organization set
      if (connectionData.organizationId) {
        await this.redis.sadd(
          `org:${connectionData.organizationId}:connections`,
          connectionData.id
        );
      }

      // Add to user set
      if (connectionData.userId) {
        await this.redis.sadd(
          `user:${connectionData.userId}:connections`,
          connectionData.id
        );
      }

      // Track connection metrics
      await this.redis.incr('metrics:connections:total');
      await this.redis.incr(`metrics:connections:${connectionData.clientType}`);

      this.logger.log(`Connection registered: ${connectionData.id}`);
      return connection;

    } catch (error) {
      this.logger.error(`Failed to register connection ${connectionData.id}:`, error);
      throw error;
    }
  }

  async unregisterConnection(connectionId: string): Promise<void> {
    try {
      // Get connection data
      const connection = await this.getConnection(connectionId);
      if (!connection) {
        this.logger.warn(`Connection not found for unregistration: ${connectionId}`);
        return;
      }

      // Update database
      await this.prisma.apiXConnection.update({
        where: { id: connectionId },
        data: {
          status: ConnectionStatus.DISCONNECTED,
          updatedAt: new Date(),
        },
      });

      // Remove from Redis
      await this.redis.del(`connection:${connectionId}`);

      // Remove from organization set
      if (connection.organizationId) {
        await this.redis.srem(
          `org:${connection.organizationId}:connections`,
          connectionId
        );
      }

      // Remove from user set
      if (connection.userId) {
        await this.redis.srem(
          `user:${connection.userId}:connections`,
          connectionId
        );
      }

      // Update metrics
      await this.redis.decr('metrics:connections:total');
      await this.redis.decr(`metrics:connections:${connection.clientType}`);

      this.logger.log(`Connection unregistered: ${connectionId}`);

    } catch (error) {
      this.logger.error(`Failed to unregister connection ${connectionId}:`, error);
      throw error;
    }
  }

  async getConnection(connectionId: string): Promise<ApiXConnection | null> {
    try {
      // Try Redis first
      const cached = await this.redis.get(`connection:${connectionId}`);
      if (cached) {
        return JSON.parse(cached);
      }

      // Fallback to database
      const connection = await this.prisma.apiXConnection.findUnique({
        where: { id: connectionId },
      });

      if (connection) {
        // Cache for future requests
        await this.redis.setex(
          `connection:${connectionId}`,
          3600,
          JSON.stringify(connection)
        );
      }

      return connection;

    } catch (error) {
      this.logger.error(`Failed to get connection ${connectionId}:`, error);
      return null;
    }
  }

  async updateHeartbeat(connectionId: string): Promise<void> {
    try {
      const now = new Date();

      // Update database
      await this.prisma.apiXConnection.update({
        where: { id: connectionId },
        data: {
          lastHeartbeat: now,
          status: ConnectionStatus.CONNECTED,
        },
      });

      // Update Redis cache
      const connection = await this.getConnection(connectionId);
      if (connection) {
        connection.lastHeartbeat = now;
        connection.status = ConnectionStatus.CONNECTED;
        await this.redis.setex(
          `connection:${connectionId}`,
          3600,
          JSON.stringify(connection)
        );
      }

    } catch (error) {
      this.logger.error(`Failed to update heartbeat for ${connectionId}:`, error);
    }
  }

  async checkHeartbeats(): Promise<void> {
    try {
      const cutoffTime = new Date(Date.now() - this.HEARTBEAT_TIMEOUT);

      // Find stale connections
      const staleConnections = await this.prisma.apiXConnection.findMany({
        where: {
          lastHeartbeat: { lt: cutoffTime },
          status: { in: [ConnectionStatus.CONNECTED, ConnectionStatus.RECONNECTING] },
        },
      });

      // Mark as disconnected
      for (const connection of staleConnections) {
        await this.unregisterConnection(connection.id);
        
        // Audit log
        await this.auditLogger.logConnection({
          connectionId: connection.id,
          userId: connection.userId,
          organizationId: connection.organizationId,
          action: 'HEARTBEAT_TIMEOUT',
          metadata: {
            lastHeartbeat: connection.lastHeartbeat,
            timeout: this.HEARTBEAT_TIMEOUT,
          },
        });
      }

      if (staleConnections.length > 0) {
        this.logger.log(`Cleaned up ${staleConnections.length} stale connections`);
      }

    } catch (error) {
      this.logger.error('Failed to check heartbeats:', error);
    }
  }

  async getUserConnections(userId: string): Promise<string[]> {
    try {
      return await this.redis.smembers(`user:${userId}:connections`);
    } catch (error) {
      this.logger.error(`Failed to get user connections for ${userId}:`, error);
      return [];
    }
  }

  async getOrganizationConnections(organizationId: string): Promise<string[]> {
    try {
      return await this.redis.smembers(`org:${organizationId}:connections`);
    } catch (error) {
      this.logger.error(`Failed to get organization connections for ${organizationId}:`, error);
      return [];
    }
  }

  async getConnectionsByClientType(clientType: ClientType, organizationId?: string): Promise<string[]> {
    try {
      const connections = await this.prisma.apiXConnection.findMany({
        where: {
          clientType,
          organizationId,
          status: ConnectionStatus.CONNECTED,
        },
        select: { id: true },
      });

      return connections.map(c => c.id);

    } catch (error) {
      this.logger.error(`Failed to get connections by client type ${clientType}:`, error);
      return [];
    }
  }

  async getConnectionStats(organizationId?: string): Promise<{
    total: number;
    connected: number;
    disconnected: number;
    reconnecting: number;
    suspended: number;
    byClientType: Record<string, number>;
    byOrganization: Record<string, number>;
  }> {
    try {
      const where = organizationId ? { organizationId } : {};

      const [
        total,
        byStatus,
        byClientType,
        byOrganization,
      ] = await Promise.all([
        this.prisma.apiXConnection.count({ where }),
        this.prisma.apiXConnection.groupBy({
          by: ['status'],
          where,
          _count: { status: true },
        }),
        this.prisma.apiXConnection.groupBy({
          by: ['clientType'],
          where,
          _count: { clientType: true },
        }),
        organizationId ? Promise.resolve([]) : this.prisma.apiXConnection.groupBy({
          by: ['organizationId'],
          where: { organizationId: { not: null } },
          _count: { organizationId: true },
        }),
      ]);

      return {
        total,
        connected: byStatus.find(s => s.status === ConnectionStatus.CONNECTED)?._count.status || 0,
        disconnected: byStatus.find(s => s.status === ConnectionStatus.DISCONNECTED)?._count.status || 0,
        reconnecting: byStatus.find(s => s.status === ConnectionStatus.RECONNECTING)?._count.status || 0,
        suspended: byStatus.find(s => s.status === ConnectionStatus.SUSPENDED)?._count.status || 0,
        byClientType: byClientType.reduce((acc, item) => {
          acc[item.clientType] = item._count.clientType;
          return acc;
        }, {} as Record<string, number>),
        byOrganization: byOrganization.reduce((acc, item) => {
          if (item.organizationId) {
            acc[item.organizationId] = item._count.organizationId;
          }
          return acc;
        }, {} as Record<string, number>),
      };

    } catch (error) {
      this.logger.error('Failed to get connection stats:', error);
      return {
        total: 0,
        connected: 0,
        disconnected: 0,
        reconnecting: 0,
        suspended: 0,
        byClientType: {},
        byOrganization: {},
      };
    }
  }

  async suspendConnection(connectionId: string, reason: string): Promise<void> {
    try {
      await this.prisma.apiXConnection.update({
        where: { id: connectionId },
        data: {
          status: ConnectionStatus.SUSPENDED,
          metadata: {
            suspendedAt: new Date().toISOString(),
            suspendReason: reason,
          },
        },
      });

      // Update Redis cache
      const connection = await this.getConnection(connectionId);
      if (connection) {
        connection.status = ConnectionStatus.SUSPENDED;
        await this.redis.setex(
          `connection:${connectionId}`,
          3600,
          JSON.stringify(connection)
        );
      }

      this.logger.log(`Connection suspended: ${connectionId} - ${reason}`);

    } catch (error) {
      this.logger.error(`Failed to suspend connection ${connectionId}:`, error);
      throw error;
    }
  }

  async resumeConnection(connectionId: string): Promise<void> {
    try {
      await this.prisma.apiXConnection.update({
        where: { id: connectionId },
        data: {
          status: ConnectionStatus.CONNECTED,
          lastHeartbeat: new Date(),
        },
      });

      // Update Redis cache
      const connection = await this.getConnection(connectionId);
      if (connection) {
        connection.status = ConnectionStatus.CONNECTED;
        connection.lastHeartbeat = new Date();
        await this.redis.setex(
          `connection:${connectionId}`,
          3600,
          JSON.stringify(connection)
        );
      }

      this.logger.log(`Connection resumed: ${connectionId}`);

    } catch (error) {
      this.logger.error(`Failed to resume connection ${connectionId}:`, error);
      throw error;
    }
  }

  private async performCleanup(): Promise<void> {
    try {
      // Remove old disconnected connections
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

      const deletedCount = await this.prisma.apiXConnection.deleteMany({
        where: {
          status: ConnectionStatus.DISCONNECTED,
          updatedAt: { lt: cutoffTime },
        },
      });

      if (deletedCount.count > 0) {
        this.logger.log(`Cleaned up ${deletedCount.count} old disconnected connections`);
      }

    } catch (error) {
      this.logger.error('Failed to perform connection cleanup:', error);
    }
  }

  async getConnectionLifecycle(connectionId: string): Promise<{
    connection: ApiXConnection | null;
    events: any[];
    subscriptions: any[];
    metrics: any[];
  }> {
    try {
      const [connection, events, subscriptions, metrics] = await Promise.all([
        this.getConnection(connectionId),
        this.prisma.apiXEvent.findMany({
          where: { sessionId: connectionId },
          orderBy: { createdAt: 'desc' },
          take: 50,
        }),
        this.prisma.apiXSubscription.findMany({
          where: { connectionId },
          include: { channel: true },
        }),
        this.prisma.apiXMetrics.findMany({
          where: { connectionId },
          orderBy: { timestamp: 'desc' },
          take: 100,
        }),
      ]);

      return {
        connection,
        events,
        subscriptions,
        metrics,
      };

    } catch (error) {
      this.logger.error(`Failed to get connection lifecycle for ${connectionId}:`, error);
      return {
        connection: null,
        events: [],
        subscriptions: [],
        metrics: [],
      };
    }
  }
}