import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ApiXService } from './apix.service';
import { EventRouterService } from './event-router.service';
import { ConnectionManagerService } from './connection-manager.service';
import { SubscriptionManagerService } from './subscription-manager.service';
import { AuditLoggerService } from './audit-logger.service';
import { LatencyTrackerService } from './latency-tracker.service';
import {
  ApiXConnectionSchema,
  ApiXEventSchema,
  ApiXSubscriptionSchema,
  ApiXHeartbeatSchema,
  APIX_EVENTS,
  ClientType,
  ConnectionStatus,
} from './apix.types';
import { WsJwtGuard } from '../auth/guards/ws-jwt.guard';

@Injectable()
@WebSocketGateway({
  cors: {
    origin: '*',
    credentials: true,
  },
  transports: ['websocket'],
  pingTimeout: 60000,
  pingInterval: 25000,
})
export class ApiXGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ApiXGateway.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly apiXService: ApiXService,
    private readonly eventRouter: EventRouterService,
    private readonly connectionManager: ConnectionManagerService,
    private readonly subscriptionManager: SubscriptionManagerService,
    private readonly auditLogger: AuditLoggerService,
    private readonly latencyTracker: LatencyTrackerService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('APIX WebSocket Gateway initialized');
    this.eventRouter.setServer(server);
    
    // Setup heartbeat monitoring
    setInterval(() => {
      this.connectionManager.checkHeartbeats();
    }, 30000);
  }

  async handleConnection(client: Socket) {
    try {
      const startTime = Date.now();
      
      // Extract authentication token
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        this.logger.warn(`Connection rejected: No token provided - ${client.id}`);
        client.emit('error', {
          code: 'AUTH_REQUIRED',
          message: 'Authentication token required',
          timestamp: Date.now(),
        });
        client.disconnect();
        return;
      }

      // Verify JWT token
      let payload: any;
      try {
        payload = this.jwtService.verify(token);
      } catch (error) {
        this.logger.warn(`Connection rejected: Invalid token - ${client.id}`);
        client.emit('error', {
          code: 'AUTH_INVALID',
          message: 'Invalid authentication token',
          timestamp: Date.now(),
        });
        client.disconnect();
        return;
      }

      // Extract connection metadata
      const connectionData = {
        sessionId: client.id,
        clientType: (client.handshake.query?.clientType as ClientType) || ClientType.WEB_APP,
        authentication: {
          token,
          organizationId: payload.organizationId,
        },
        subscriptions: [],
        metadata: {
          userAgent: client.handshake.headers['user-agent'],
          ipAddress: client.handshake.address,
          connectedAt: new Date().toISOString(),
        },
      };

      // Validate connection data
      const validatedData = ApiXConnectionSchema.parse(connectionData);

      // Register connection
      const connection = await this.connectionManager.registerConnection({
        id: client.id,
        sessionId: client.id,
        organizationId: payload.organizationId,
        userId: payload.sub,
        clientType: validatedData.clientType,
        channels: [],
        metadata: validatedData.metadata,
        connectedAt: new Date(),
        lastHeartbeat: new Date(),
        status: ConnectionStatus.CONNECTED,
        ipAddress: client.handshake.address,
        userAgent: client.handshake.headers['user-agent'],
      });

      // Store connection context in socket
      client.data = {
        connection,
        userId: payload.sub,
        organizationId: payload.organizationId,
        connectedAt: Date.now(),
      };

      // Track latency
      const latency = Date.now() - startTime;
      await this.latencyTracker.recordLatency(client.id, 'connection', latency);

      // Emit connection established event
      client.emit(APIX_EVENTS.CONNECTION_ESTABLISHED, {
        connectionId: client.id,
        timestamp: Date.now(),
        latency,
      });

      // Audit log
      await this.auditLogger.logConnection({
        connectionId: client.id,
        userId: payload.sub,
        organizationId: payload.organizationId,
        action: 'CONNECTED',
        metadata: {
          clientType: validatedData.clientType,
          ipAddress: client.handshake.address,
          userAgent: client.handshake.headers['user-agent'],
        },
      });

      this.logger.log(`Client connected: ${client.id} (User: ${payload.sub}, Org: ${payload.organizationId})`);

    } catch (error) {
      this.logger.error(`Connection error for ${client.id}:`, error);
      client.emit('error', {
        code: 'CONNECTION_ERROR',
        message: 'Failed to establish connection',
        details: error.message,
        timestamp: Date.now(),
      });
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    try {
      const connectionData = client.data;
      
      if (connectionData) {
        // Unregister connection
        await this.connectionManager.unregisterConnection(client.id);
        
        // Remove all subscriptions
        await this.subscriptionManager.removeAllSubscriptions(client.id);
        
        // Audit log
        await this.auditLogger.logConnection({
          connectionId: client.id,
          userId: connectionData.userId,
          organizationId: connectionData.organizationId,
          action: 'DISCONNECTED',
          metadata: {
            duration: Date.now() - connectionData.connectedAt,
          },
        });

        this.logger.log(`Client disconnected: ${client.id}`);
      }
    } catch (error) {
      this.logger.error(`Disconnect error for ${client.id}:`, error);
    }
  }

  @SubscribeMessage('subscribe')
  async handleSubscribe(
    @MessageBody() data: any,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const startTime = Date.now();
      const validatedData = ApiXSubscriptionSchema.parse(data);
      const connectionData = client.data;

      if (!connectionData) {
        throw new Error('Connection not authenticated');
      }

      // Add subscriptions
      for (const channel of validatedData.channels) {
        await this.subscriptionManager.addSubscription({
          connectionId: client.id,
          channelName: channel,
          filters: validatedData.filters,
          organizationId: connectionData.organizationId,
          userId: connectionData.userId,
        });

        // Join socket room
        client.join(channel);
      }

      // Track latency
      const latency = Date.now() - startTime;
      await this.latencyTracker.recordLatency(client.id, 'subscription', latency);

      // Emit confirmation
      client.emit(APIX_EVENTS.SUBSCRIPTION_ADDED, {
        channels: validatedData.channels,
        timestamp: Date.now(),
        latency,
      });

      // Audit log
      await this.auditLogger.logSubscription({
        connectionId: client.id,
        userId: connectionData.userId,
        organizationId: connectionData.organizationId,
        action: 'SUBSCRIBED',
        channels: validatedData.channels,
      });

    } catch (error) {
      this.logger.error(`Subscribe error for ${client.id}:`, error);
      client.emit('error', {
        code: 'SUBSCRIPTION_ERROR',
        message: 'Failed to subscribe to channels',
        details: error.message,
        timestamp: Date.now(),
      });
    }
  }

  @SubscribeMessage('unsubscribe')
  async handleUnsubscribe(
    @MessageBody() data: { channels: string[] },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const connectionData = client.data;

      if (!connectionData) {
        throw new Error('Connection not authenticated');
      }

      // Remove subscriptions
      for (const channel of data.channels) {
        await this.subscriptionManager.removeSubscription(client.id, channel);
        client.leave(channel);
      }

      // Emit confirmation
      client.emit(APIX_EVENTS.SUBSCRIPTION_REMOVED, {
        channels: data.channels,
        timestamp: Date.now(),
      });

      // Audit log
      await this.auditLogger.logSubscription({
        connectionId: client.id,
        userId: connectionData.userId,
        organizationId: connectionData.organizationId,
        action: 'UNSUBSCRIBED',
        channels: data.channels,
      });

    } catch (error) {
      this.logger.error(`Unsubscribe error for ${client.id}:`, error);
      client.emit('error', {
        code: 'UNSUBSCRIPTION_ERROR',
        message: 'Failed to unsubscribe from channels',
        details: error.message,
        timestamp: Date.now(),
      });
    }
  }

  @SubscribeMessage('ping')
  async handlePing(
    @MessageBody() data: any,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const heartbeatData = ApiXHeartbeatSchema.parse(data);
      
      // Update heartbeat
      await this.connectionManager.updateHeartbeat(client.id);
      
      // Respond with pong
      client.emit(APIX_EVENTS.HEARTBEAT_PONG, {
        timestamp: Date.now(),
        connectionId: client.id,
      });

    } catch (error) {
      this.logger.error(`Ping error for ${client.id}:`, error);
    }
  }

  @SubscribeMessage('event')
  async handleEvent(
    @MessageBody() data: any,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const startTime = Date.now();
      const validatedData = ApiXEventSchema.parse(data);
      const connectionData = client.data;

      if (!connectionData) {
        throw new Error('Connection not authenticated');
      }

      // Route event through the event router
      await this.eventRouter.routeEvent({
        ...validatedData,
        sessionId: client.id,
        organizationId: connectionData.organizationId,
        userId: connectionData.userId,
        correlationId: validatedData.metadata?.correlation_id,
      });

      // Track latency
      const latency = Date.now() - startTime;
      await this.latencyTracker.recordLatency(client.id, validatedData.type, latency);

      // Emit confirmation if acknowledgment requested
      if (validatedData.metadata?.acknowledgment) {
        client.emit(APIX_EVENTS.MESSAGE_SENT, {
          eventType: validatedData.type,
          timestamp: Date.now(),
          latency,
        });
      }

    } catch (error) {
      this.logger.error(`Event error for ${client.id}:`, error);
      client.emit('error', {
        code: 'EVENT_ERROR',
        message: 'Failed to process event',
        details: error.message,
        timestamp: Date.now(),
      });
    }
  }

  // Method to broadcast events to specific channels
  async broadcastToChannel(channel: string, event: any, organizationId?: string) {
    try {
      // Get channel subscribers
      const subscribers = await this.subscriptionManager.getChannelSubscribers(channel, organizationId);
      
      // Broadcast to each subscriber
      for (const subscriber of subscribers) {
        const socket = this.server.sockets.sockets.get(subscriber.connectionId);
        if (socket) {
          socket.emit('event', event);
        }
      }

      // Track metrics
      await this.latencyTracker.recordThroughput(channel, subscribers.length);

    } catch (error) {
      this.logger.error(`Broadcast error for channel ${channel}:`, error);
    }
  }

  // Method to send event to specific connection
  async sendToConnection(connectionId: string, event: any) {
    try {
      const socket = this.server.sockets.sockets.get(connectionId);
      if (socket) {
        socket.emit('event', event);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`Send error for connection ${connectionId}:`, error);
      return false;
    }
  }
}