import { Injectable, Logger } from '@nestjs/common';
import { EventRouterService } from './event-router.service';
import { ConnectionManagerService } from './connection-manager.service';
import { SubscriptionManagerService } from './subscription-manager.service';
import { MessageQueueManagerService } from './message-queue-manager.service';
import { RetryManagerService } from './retry-manager.service';
import { LatencyTrackerService } from './latency-tracker.service';
import { AuditLoggerService } from './audit-logger.service';
import { ApiXGateway } from './apix.gateway';
import { ApiXEvent, ApiXConnection, APIX_EVENTS, APIX_CHANNELS } from './apix.types';

@Injectable()
export class ApiXService {
  private readonly logger = new Logger(ApiXService.name);

  constructor(
    private readonly eventRouter: EventRouterService,
    private readonly connectionManager: ConnectionManagerService,
    private readonly subscriptionManager: SubscriptionManagerService,
    private readonly messageQueueManager: MessageQueueManagerService,
    private readonly retryManager: RetryManagerService,
    private readonly latencyTracker: LatencyTrackerService,
    private readonly auditLogger: AuditLoggerService,
  ) {}

  async broadcastEvent(event: Partial<ApiXEvent>): Promise<void> {
    try {
      await this.eventRouter.routeEvent({
        id: event.id || `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        eventType: event.eventType!,
        channel: event.channel!,
        payload: event.payload,
        sessionId: event.sessionId,
        organizationId: event.organizationId,
        userId: event.userId,
        acknowledgment: event.acknowledgment || false,
        retryCount: 0,
        maxRetries: 3,
        status: 'PENDING',
        createdAt: new Date(),
        metadata: event.metadata,
        correlationId: event.correlationId,
      });
    } catch (error) {
      this.logger.error('Failed to broadcast event:', error);
      throw error;
    }
  }

  async sendToConnection(connectionId: string, event: any): Promise<boolean> {
    try {
      const connection = await this.connectionManager.getConnection(connectionId);
      if (!connection) {
        this.logger.warn(`Connection not found: ${connectionId}`);
        return false;
      }

      // Queue the message for delivery
      await this.messageQueueManager.enqueue('events', {
        type: 'direct_message',
        payload: {
          connectionId,
          event,
        },
        organizationId: connection.organizationId,
        userId: connection.userId,
      });

      return true;
    } catch (error) {
      this.logger.error(`Failed to send to connection ${connectionId}:`, error);
      return false;
    }
  }

  async sendToUser(userId: string, event: any, organizationId?: string): Promise<number> {
    try {
      const connections = await this.connectionManager.getUserConnections(userId);
      let sentCount = 0;

      for (const connectionId of connections) {
        const connection = await this.connectionManager.getConnection(connectionId);
        if (connection && (!organizationId || connection.organizationId === organizationId)) {
          const sent = await this.sendToConnection(connectionId, event);
          if (sent) sentCount++;
        }
      }

      return sentCount;
    } catch (error) {
      this.logger.error(`Failed to send to user ${userId}:`, error);
      return 0;
    }
  }

  async sendToOrganization(organizationId: string, event: any): Promise<number> {
    try {
      const connections = await this.connectionManager.getOrganizationConnections(organizationId);
      let sentCount = 0;

      for (const connectionId of connections) {
        const sent = await this.sendToConnection(connectionId, event);
        if (sent) sentCount++;
      }

      return sentCount;
    } catch (error) {
      this.logger.error(`Failed to send to organization ${organizationId}:`, error);
      return 0;
    }
  }

  async getSystemStats(): Promise<{
    connections: any;
    subscriptions: any;
    queues: any;
    retries: any;
    latency: any;
  }> {
    try {
      const [connections, subscriptions, queues, retries, latency] = await Promise.all([
        this.connectionManager.getConnectionStats(),
        this.subscriptionManager.getSubscriptionStats(),
        this.messageQueueManager.getAllQueueStats(),
        this.retryManager.getRetryStats(),
        this.latencyTracker.getSystemLatencyStats(),
      ]);

      return {
        connections,
        subscriptions,
        queues,
        retries,
        latency,
      };
    } catch (error) {
      this.logger.error('Failed to get system stats:', error);
      return {
        connections: {},
        subscriptions: {},
        queues: {},
        retries: {},
        latency: {},
      };
    }
  }

  async getOrganizationStats(organizationId: string): Promise<{
    connections: any;
    subscriptions: any;
    retries: any;
    latency: any;
  }> {
    try {
      const [connections, subscriptions, retries, latency] = await Promise.all([
        this.connectionManager.getConnectionStats(organizationId),
        this.subscriptionManager.getSubscriptionStats(organizationId),
        this.retryManager.getRetryStats(organizationId),
        this.latencyTracker.getOrganizationLatencyStats(organizationId),
      ]);

      return {
        connections,
        subscriptions,
        retries,
        latency,
      };
    } catch (error) {
      this.logger.error(`Failed to get organization stats for ${organizationId}:`, error);
      return {
        connections: {},
        subscriptions: {},
        retries: {},
        latency: {},
      };
    }
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    components: Record<string, { status: string; details?: any }>;
    timestamp: number;
  }> {
    const components: Record<string, { status: string; details?: any }> = {};
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    try {
      // Check connection manager
      const connectionStats = await this.connectionManager.getConnectionStats();
      components.connectionManager = {
        status: 'healthy',
        details: { totalConnections: connectionStats.total, connectedConnections: connectionStats.connected },
      };
    } catch (error) {
      components.connectionManager = { status: 'unhealthy', details: error.message };
      overallStatus = 'unhealthy';
    }

    try {
      // Check subscription manager
      const subscriptionStats = await this.subscriptionManager.getSubscriptionStats();
      components.subscriptionManager = {
        status: 'healthy',
        details: { totalSubscriptions: subscriptionStats.totalSubscriptions },
      };
    } catch (error) {
      components.subscriptionManager = { status: 'unhealthy', details: error.message };
      overallStatus = 'unhealthy';
    }

    try {
      // Check message queue manager
      const queueStats = await this.messageQueueManager.getAllQueueStats();
      const hasFailedJobs = Object.values(queueStats).some((stats: any) => stats.failed > 0);
      components.messageQueueManager = {
        status: hasFailedJobs ? 'degraded' : 'healthy',
        details: queueStats,
      };
      if (hasFailedJobs && overallStatus === 'healthy') {
        overallStatus = 'degraded';
      }
    } catch (error) {
      components.messageQueueManager = { status: 'unhealthy', details: error.message };
      overallStatus = 'unhealthy';
    }

    try {
      // Check retry manager
      const retryStats = await this.retryManager.getRetryStats();
      const hasDeadLetterItems = retryStats.deadLetterQueue > 0;
      components.retryManager = {
        status: hasDeadLetterItems ? 'degraded' : 'healthy',
        details: retryStats,
      };
      if (hasDeadLetterItems && overallStatus === 'healthy') {
        overallStatus = 'degraded';
      }
    } catch (error) {
      components.retryManager = { status: 'unhealthy', details: error.message };
      overallStatus = 'unhealthy';
    }

    try {
      // Check latency tracker
      const latencyStats = await this.latencyTracker.getSystemLatencyStats();
      components.latencyTracker = {
        status: 'healthy',
        details: latencyStats,
      };
    } catch (error) {
      components.latencyTracker = { status: 'unhealthy', details: error.message };
      overallStatus = 'unhealthy';
    }

    return {
      status: overallStatus,
      components,
      timestamp: Date.now(),
    };
  }

  async performMaintenance(): Promise<void> {
    try {
      this.logger.log('Starting ApiX maintenance tasks...');

      // Cleanup stale connections
      await this.connectionManager.checkHeartbeats();

      // Cleanup stale subscriptions
      await this.subscriptionManager.cleanupStaleSubscriptions();

      // Clean queues
      const queueNames = ['events', 'notifications', 'analytics', 'billing'];
      for (const queueName of queueNames) {
        await this.messageQueueManager.cleanQueue(queueName, 24 * 60 * 60 * 1000); // 24 hours
      }

      this.logger.log('ApiX maintenance tasks completed');
    } catch (error) {
      this.logger.error('Failed to perform maintenance:', error);
      throw error;
    }
  }

  // Event emission helpers
  async emitAgentStatusUpdate(agentId: string, status: string, organizationId: string): Promise<void> {
    await this.broadcastEvent({
      eventType: APIX_EVENTS.AGENT_STATUS_UPDATE,
      channel: APIX_CHANNELS.AGENTS,
      payload: { agentId, status, timestamp: Date.now() },
      organizationId,
    });
  }

  async emitToolCallStart(toolId: string, input: any, organizationId: string, userId?: string): Promise<void> {
    await this.broadcastEvent({
      eventType: APIX_EVENTS.TOOL_CALL_START,
      channel: APIX_CHANNELS.TOOLS,
      payload: { toolId, input, timestamp: Date.now() },
      organizationId,
      userId,
    });
  }

  async emitToolCallResult(toolId: string, result: any, organizationId: string, userId?: string): Promise<void> {
    await this.broadcastEvent({
      eventType: APIX_EVENTS.TOOL_CALL_RESULT,
      channel: APIX_CHANNELS.TOOLS,
      payload: { toolId, result, timestamp: Date.now() },
      organizationId,
      userId,
    });
  }

  async emitToolCallError(toolId: string, error: string, organizationId: string, userId?: string): Promise<void> {
    await this.broadcastEvent({
      eventType: APIX_EVENTS.TOOL_CALL_ERROR,
      channel: APIX_CHANNELS.TOOLS,
      payload: { toolId, error, timestamp: Date.now() },
      organizationId,
      userId,
    });
  }

  async emitWorkflowStateChange(workflowId: string, state: string, organizationId: string): Promise<void> {
    await this.broadcastEvent({
      eventType: APIX_EVENTS.WORKFLOW_STATE_CHANGE,
      channel: APIX_CHANNELS.WORKFLOWS,
      payload: { workflowId, state, timestamp: Date.now() },
      organizationId,
    });
  }

  async emitProviderHealthUpdate(providerId: string, health: any, organizationId: string): Promise<void> {
    await this.broadcastEvent({
      eventType: APIX_EVENTS.PROVIDER_HEALTH_UPDATE,
      channel: APIX_CHANNELS.PROVIDERS,
      payload: { providerId, health, timestamp: Date.now() },
      organizationId,
    });
  }

  async emitSystemNotification(message: string, level: 'info' | 'warning' | 'error', organizationId?: string): Promise<void> {
    await this.broadcastEvent({
      eventType: APIX_EVENTS.SYSTEM_NOTIFICATION,
      channel: APIX_CHANNELS.SYSTEM,
      payload: { message, level, timestamp: Date.now() },
      organizationId,
    });
  }
}