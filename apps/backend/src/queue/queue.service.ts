import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

@Injectable()
export class QueueService {
  constructor(
    @InjectQueue('notifications') private notificationQueue: Queue,
    @InjectQueue('analytics') private analyticsQueue: Queue,
    @InjectQueue('file-processing') private fileProcessingQueue: Queue,
  ) {}

  async addNotificationJob(data: any, options?: any) {
    return this.notificationQueue.add('send-notification', data, {
      delay: options?.delay || 0,
      attempts: options?.attempts || 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      ...options,
    });
  }

  async addAnalyticsJob(data: any, options?: any) {
    return this.analyticsQueue.add('process-analytics', data, {
      delay: options?.delay || 0,
      attempts: options?.attempts || 3,
      ...options,
    });
  }

  async addFileProcessingJob(data: any, options?: any) {
    return this.fileProcessingQueue.add('process-file', data, {
      delay: options?.delay || 0,
      attempts: options?.attempts || 3,
      ...options,
    });
  }

  async getQueueStats() {
    const [notificationStats, analyticsStats, fileProcessingStats] = await Promise.all([
      this.getQueueInfo(this.notificationQueue),
      this.getQueueInfo(this.analyticsQueue),
      this.getQueueInfo(this.fileProcessingQueue),
    ]);

    return {
      notifications: notificationStats,
      analytics: analyticsStats,
      fileProcessing: fileProcessingStats,
    };
  }

  private async getQueueInfo(queue: Queue) {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
    };
  }
}