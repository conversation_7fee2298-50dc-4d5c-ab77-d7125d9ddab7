import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { MulterModule } from '@nestjs/platform-express';
import { WidgetController } from './widget.controller';
import { WidgetAuthService } from './widget-auth.service';
import { WidgetConfigService } from './widget-config.service';
import { DatabaseModule } from '../database/database.module';
import { RedisModule } from '../redis/redis.module';
import { ApixModule } from '../apix/apix.module';
import { memoryStorage } from 'multer';

@Module({
  imports: [
    DatabaseModule,
    RedisModule,
    ApixModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
    MulterModule.register({
      storage: memoryStorage(),
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit for voice files
      },
      fileFilter: (req, file, callback) => {
        // Only allow audio files
        if (file.mimetype.startsWith('audio/')) {
          callback(null, true);
        } else {
          callback(new Error('Only audio files are allowed'), false);
        }
      },
    }),
  ],
  controllers: [WidgetController],
  providers: [WidgetAuthService, WidgetConfigService],
  exports: [WidgetAuthService, WidgetConfigService],
})
export class WidgetModule {}