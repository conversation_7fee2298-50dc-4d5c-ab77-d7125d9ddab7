import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Headers,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request, Response } from 'express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { WidgetAuthService } from './widget-auth.service';
import { WidgetConfigService } from './widget-config.service';
import { ApixService } from '../apix/apix.service';
import { 
  WidgetConfigSchema,
  WidgetThemeSchema,
  WidgetAnalyticsEventSchema,
  ChatMessageSchema,
  VoiceInputSchema,
  WIDGET_EVENTS,
  WIDGET_CHANNELS,
  WidgetErrorType,
} from './widget.types';
import { z } from 'zod';

@Controller('widgets')
export class WidgetController {
  constructor(
    private readonly widgetAuth: WidgetAuthService,
    private readonly widgetConfig: WidgetConfigService,
    private readonly apix: ApixService,
  ) {}

  // Widget Configuration Endpoints
  @Post()
  @UseGuards(JwtAuthGuard)
  async createWidget(@Body() body: any, @Req() req: any) {
    const data = WidgetConfigSchema.parse(body);
    const organizationId = req.user.organizationId;

    const widget = await this.widgetConfig.createWidget(organizationId, data);
    
    // Generate API key for the widget
    const apiKey = await this.widgetAuth.generateApiKey(
      widget.id,
      organizationId,
      {
        canSendMessages: true,
        canReceiveMessages: true,
        canUploadFiles: data.settings.features.fileUpload,
        canUseVoice: data.settings.features.voiceInput,
        canAccessAnalytics: false,
        canModifySettings: false,
      }
    );

    return {
      ...widget,
      apiKey,
    };
  }

  @Get(':widgetId')
  @UseGuards(JwtAuthGuard)
  async getWidget(@Param('widgetId') widgetId: string) {
    return this.widgetConfig.getWidget(widgetId);
  }

  @Put(':widgetId')
  @UseGuards(JwtAuthGuard)
  async updateWidget(@Param('widgetId') widgetId: string, @Body() body: any) {
    const data = WidgetConfigSchema.partial().parse(body);
    return this.widgetConfig.updateWidget(widgetId, data);
  }

  @Delete(':widgetId')
  @UseGuards(JwtAuthGuard)
  async deleteWidget(@Param('widgetId') widgetId: string) {
    return this.widgetConfig.deleteWidget(widgetId);
  }

  @Get('organization/:organizationId')
  @UseGuards(JwtAuthGuard)
  async getWidgetsByOrganization(@Param('organizationId') organizationId: string) {
    return this.widgetConfig.getWidgetsByOrganization(organizationId);
  }

  // Widget Session Endpoints
  @Post(':widgetId/session')
  async createSession(
    @Param('widgetId') widgetId: string,
    @Headers('x-api-key') apiKey: string,
    @Headers('origin') origin: string,
    @Body() body: any,
    @Req() req: Request,
  ) {
    // Validate API key
    const auth = await this.widgetAuth.validateApiKey(apiKey);
    
    if (auth.widgetId !== widgetId) {
      throw new UnauthorizedException('API key does not match widget');
    }

    // Validate domain
    if (origin) {
      const domain = new URL(origin).hostname;
      const isValidDomain = await this.widgetAuth.validateDomain(widgetId, domain);
      if (!isValidDomain) {
        throw new UnauthorizedException('Domain not allowed');
      }
    }

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Generate session token
    const token = await this.widgetAuth.generateWidgetToken(
      widgetId,
      sessionId,
      auth.organizationId,
    );

    // Track session start
    await this.widgetConfig.trackAnalytics({
      widgetId,
      eventType: 'session_started',
      eventData: {
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip,
        referrer: req.headers.referer,
      },
      sessionId,
    });

    // Emit APIX event
    await this.apix.emit({
      type: WIDGET_EVENTS.SESSION_STARTED,
      channel: `${WIDGET_CHANNELS.SESSIONS}.${auth.organizationId}`,
      payload: {
        widgetId,
        sessionId,
        metadata: body.metadata,
      },
    });

    return {
      sessionId,
      token,
      widgetId,
    };
  }

  @Delete(':widgetId/session/:sessionId')
  async endSession(
    @Param('widgetId') widgetId: string,
    @Param('sessionId') sessionId: string,
    @Headers('authorization') authorization: string,
  ) {
    const token = authorization?.replace('Bearer ', '');
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    const auth = await this.widgetAuth.validateWidgetToken(token);
    
    if (auth.widgetId !== widgetId || auth.sessionId !== sessionId) {
      throw new UnauthorizedException('Invalid token for this session');
    }

    // Revoke session
    await this.widgetAuth.revokeWidgetSession(sessionId);

    // Track session end
    await this.widgetConfig.trackAnalytics({
      widgetId,
      eventType: 'session_ended',
      eventData: {},
      sessionId,
    });

    // Emit APIX event
    await this.apix.emit({
      type: WIDGET_EVENTS.SESSION_ENDED,
      channel: `${WIDGET_CHANNELS.SESSIONS}.${auth.organizationId}`,
      payload: {
        widgetId,
        sessionId,
      },
    });

    return { success: true };
  }

  // Chat Endpoints
  @Post(':widgetId/chat')
  async sendMessage(
    @Param('widgetId') widgetId: string,
    @Headers('authorization') authorization: string,
    @Body() body: any,
    @Req() req: Request,
  ) {
    const token = authorization?.replace('Bearer ', '');
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    const auth = await this.widgetAuth.validateWidgetToken(token);
    const data = ChatMessageSchema.parse(body);

    // Check rate limit
    const canProceed = await this.widgetAuth.checkRateLimit(
      widgetId,
      data.sessionId,
      'send_message',
    );

    if (!canProceed) {
      throw new BadRequestException('Rate limit exceeded');
    }

    // Check permissions
    const canSend = await this.widgetAuth.checkPermission(widgetId, 'canSendMessages');
    if (!canSend) {
      throw new UnauthorizedException('No permission to send messages');
    }

    // Track message sent
    await this.widgetConfig.trackAnalytics({
      widgetId,
      eventType: 'message_sent',
      eventData: {
        messageLength: data.message.length,
        language: data.language,
        type: data.metadata?.type || 'text',
      },
      sessionId: data.sessionId,
    });

    // Emit APIX event
    await this.apix.emit({
      type: WIDGET_EVENTS.MESSAGE_SENT,
      channel: `${WIDGET_CHANNELS.MESSAGES}.${auth.organizationId}`,
      payload: {
        widgetId,
        sessionId: data.sessionId,
        message: data.message,
        language: data.language,
        metadata: data.metadata,
      },
    });

    // Simulate AI response (in production, this would integrate with your AI service)
    const response = await this.generateAIResponse(data.message, data.language);

    // Track message received
    await this.widgetConfig.trackAnalytics({
      widgetId,
      eventType: 'message_received',
      eventData: {
        responseLength: response.length,
        language: data.language,
      },
      sessionId: data.sessionId,
    });

    // Emit APIX event for response
    await this.apix.emit({
      type: WIDGET_EVENTS.MESSAGE_RECEIVED,
      channel: `${WIDGET_CHANNELS.MESSAGES}.${auth.organizationId}`,
      payload: {
        widgetId,
        sessionId: data.sessionId,
        response,
        language: data.language,
      },
    });

    return {
      response,
      metadata: {
        timestamp: new Date().toISOString(),
        language: data.language,
        processingTime: Math.random() * 1000 + 500, // Simulated processing time
      },
    };
  }

  // Voice Input Endpoint
  @Post(':widgetId/voice')
  @UseInterceptors(FileInterceptor('audio'))
  async processVoiceInput(
    @Param('widgetId') widgetId: string,
    @Headers('authorization') authorization: string,
    @UploadedFile() audioFile: Express.Multer.File,
    @Body() body: any,
  ) {
    const token = authorization?.replace('Bearer ', '');
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    const auth = await this.widgetAuth.validateWidgetToken(token);
    const data = VoiceInputSchema.parse(body);

    // Check permissions
    const canUseVoice = await this.widgetAuth.checkPermission(widgetId, 'canUseVoice');
    if (!canUseVoice) {
      throw new UnauthorizedException('No permission to use voice input');
    }

    if (!audioFile) {
      throw new BadRequestException('No audio file provided');
    }

    // Process voice input (in production, integrate with speech-to-text service)
    const transcript = await this.processAudioToText(audioFile, data.language);

    // Track voice input usage
    await this.widgetConfig.trackAnalytics({
      widgetId,
      eventType: 'voice_input_used',
      eventData: {
        language: data.language,
        duration: data.metadata?.duration,
        fileSize: audioFile.size,
      },
      sessionId: data.sessionId,
    });

    // Emit APIX event
    await this.apix.emit({
      type: WIDGET_EVENTS.VOICE_INPUT_PROCESSED,
      channel: `${WIDGET_CHANNELS.MESSAGES}.${auth.organizationId}`,
      payload: {
        widgetId,
        sessionId: data.sessionId,
        transcript,
        language: data.language,
      },
    });

    return {
      transcript,
      confidence: Math.random() * 0.3 + 0.7, // Simulated confidence score
      language: data.language,
    };
  }

  // Theme Endpoints
  @Post('themes')
  @UseGuards(JwtAuthGuard)
  async createTheme(@Body() body: any, @Req() req: any) {
    const data = WidgetThemeSchema.parse(body);
    const organizationId = req.user.organizationId;
    return this.widgetConfig.createTheme(organizationId, data);
  }

  @Get('themes/:themeId')
  @UseGuards(JwtAuthGuard)
  async getTheme(@Param('themeId') themeId: string) {
    return this.widgetConfig.getTheme(themeId);
  }

  @Get('organization/:organizationId/themes')
  @UseGuards(JwtAuthGuard)
  async getThemesByOrganization(@Param('organizationId') organizationId: string) {
    return this.widgetConfig.getThemesByOrganization(organizationId);
  }

  @Put('themes/:themeId')
  @UseGuards(JwtAuthGuard)
  async updateTheme(@Param('themeId') themeId: string, @Body() body: any) {
    const data = WidgetThemeSchema.partial().parse(body);
    return this.widgetConfig.updateTheme(themeId, data);
  }

  @Delete('themes/:themeId')
  @UseGuards(JwtAuthGuard)
  async deleteTheme(@Param('themeId') themeId: string) {
    return this.widgetConfig.deleteTheme(themeId);
  }

  // Analytics Endpoints
  @Post(':widgetId/analytics')
  async trackEvent(
    @Param('widgetId') widgetId: string,
    @Headers('authorization') authorization: string,
    @Body() body: any,
  ) {
    const token = authorization?.replace('Bearer ', '');
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    await this.widgetAuth.validateWidgetToken(token);
    const data = WidgetAnalyticsEventSchema.parse(body);

    return this.widgetConfig.trackAnalytics(data);
  }

  @Get(':widgetId/analytics')
  @UseGuards(JwtAuthGuard)
  async getAnalytics(
    @Param('widgetId') widgetId: string,
    @Query('eventType') eventType?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit') limit?: string,
  ) {
    const filters: any = {};
    
    if (eventType) filters.eventType = eventType;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);
    if (limit) filters.limit = parseInt(limit);

    return this.widgetConfig.getAnalytics(widgetId, filters);
  }

  @Get(':widgetId/analytics/summary')
  @UseGuards(JwtAuthGuard)
  async getAnalyticsSummary(
    @Param('widgetId') widgetId: string,
    @Query('period') period: 'day' | 'week' | 'month' = 'day',
  ) {
    return this.widgetConfig.getAnalyticsSummary(widgetId, period);
  }

  @Get(':widgetId/analytics/realtime')
  @UseGuards(JwtAuthGuard)
  async getRealTimeMetrics(@Param('widgetId') widgetId: string) {
    return this.widgetConfig.getRealTimeMetrics(widgetId);
  }

  // Widget Embed Endpoint (for iframe embedding)
  @Get(':widgetId/embed')
  async getEmbedCode(
    @Param('widgetId') widgetId: string,
    @Query('apiKey') apiKey: string,
    @Res() res: Response,
  ) {
    if (!apiKey) {
      throw new UnauthorizedException('API key required');
    }

    const auth = await this.widgetAuth.validateApiKey(apiKey);
    
    if (auth.widgetId !== widgetId) {
      throw new UnauthorizedException('API key does not match widget');
    }

    const widget = await this.widgetConfig.getWidget(widgetId);

    const embedScript = `
      (function() {
        var script = document.createElement('script');
        script.src = '${process.env.FRONTEND_URL}/widget-sdk.js';
        script.onload = function() {
          window.SynapseWidget.init({
            widgetId: '${widgetId}',
            apiKey: '${apiKey}',
            apiUrl: '${process.env.API_URL}',
            settings: ${JSON.stringify(widget.settings)}
          });
        };
        document.head.appendChild(script);
      })();
    `;

    res.setHeader('Content-Type', 'application/javascript');
    res.setHeader('Cache-Control', 'public, max-age=3600');
    res.send(embedScript);
  }

  // Health Check
  @Get(':widgetId/health')
  async healthCheck(@Param('widgetId') widgetId: string) {
    const widget = await this.widgetConfig.getWidget(widgetId);
    
    return {
      status: 'healthy',
      widgetId,
      isActive: widget.isActive,
      timestamp: new Date().toISOString(),
    };
  }

  // Private helper methods
  private async generateAIResponse(message: string, language: string): Promise<string> {
    // In production, this would integrate with your AI service
    // For now, return a simple response based on language
    const responses = {
      en: `Thank you for your message: "${message}". How can I help you further?`,
      es: `Gracias por tu mensaje: "${message}". ¿Cómo puedo ayudarte más?`,
      fr: `Merci pour votre message: "${message}". Comment puis-je vous aider davantage?`,
      de: `Danke für Ihre Nachricht: "${message}". Wie kann ich Ihnen weiterhelfen?`,
      zh: `感谢您的消息："${message}"。我还能为您做些什么？`,
    };

    return responses[language as keyof typeof responses] || responses.en;
  }

  private async processAudioToText(audioFile: Express.Multer.File, language: string): Promise<string> {
    // In production, this would integrate with a speech-to-text service
    // For now, return a simulated transcript
    return `[Simulated transcript in ${language}] Hello, this is a voice message.`;
  }
}