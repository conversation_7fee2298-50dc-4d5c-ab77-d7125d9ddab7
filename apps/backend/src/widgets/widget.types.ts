import { z } from 'zod';
import { WidgetType } from '@prisma/client';

// Widget Configuration Schema
export const WidgetConfigSchema = z.object({
  name: z.string().min(1).max(100),
  type: z.nativeEnum(WidgetType),
  settings: z.object({
    theme: z.object({
      primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i),
      position: z.enum(['bottom-right', 'bottom-left', 'top-right', 'top-left']),
      size: z.enum(['small', 'medium', 'large']),
      borderRadius: z.number().min(0).max(50).default(12),
      shadow: z.boolean().default(true),
      animation: z.boolean().default(true),
    }),
    features: z.object({
      voiceInput: z.boolean().default(false),
      fileUpload: z.boolean().default(false),
      multiLanguage: z.boolean().default(false),
      darkMode: z.boolean().default(false),
      minimizable: z.boolean().default(true),
      draggable: z.boolean().default(true),
      resizable: z.boolean().default(false),
    }),
    behavior: z.object({
      autoOpen: z.boolean().default(false),
      greeting: z.string().optional(),
      placeholder: z.string().default('Type your message...'),
      maxMessages: z.number().min(10).max(1000).default(100),
      typingIndicator: z.boolean().default(true),
      readReceipts: z.boolean().default(true),
    }),
    security: z.object({
      allowedDomains: z.array(z.string().url()).default([]),
      rateLimitPerMinute: z.number().min(1).max(1000).default(60),
      sessionTimeout: z.number().min(300).max(86400).default(3600),
      requireAuth: z.boolean().default(false),
    }),
  }),
  domains: z.array(z.string().url()),
  isActive: z.boolean().default(true),
});

export const WidgetSessionSchema = z.object({
  widgetId: z.string(),
  sessionId: z.string(),
  metadata: z.object({
    userAgent: z.string().optional(),
    ipAddress: z.string().optional(),
    referrer: z.string().optional(),
    language: z.string().default('en'),
    timezone: z.string().optional(),
    screenResolution: z.string().optional(),
  }),
});

export const WidgetAnalyticsEventSchema = z.object({
  widgetId: z.string(),
  eventType: z.enum([
    'widget_loaded',
    'widget_opened',
    'widget_closed',
    'message_sent',
    'message_received',
    'voice_input_used',
    'file_uploaded',
    'language_changed',
    'error_occurred',
    'session_started',
    'session_ended',
  ]),
  eventData: z.record(z.any()),
  sessionId: z.string().optional(),
  userId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export const WidgetThemeSchema = z.object({
  name: z.string().min(1).max(50),
  config: z.object({
    colors: z.object({
      primary: z.string().regex(/^#[0-9A-F]{6}$/i),
      secondary: z.string().regex(/^#[0-9A-F]{6}$/i),
      accent: z.string().regex(/^#[0-9A-F]{6}$/i),
      background: z.string().regex(/^#[0-9A-F]{6}$/i),
      surface: z.string().regex(/^#[0-9A-F]{6}$/i),
      text: z.string().regex(/^#[0-9A-F]{6}$/i),
      textSecondary: z.string().regex(/^#[0-9A-F]{6}$/i),
      border: z.string().regex(/^#[0-9A-F]{6}$/i),
      error: z.string().regex(/^#[0-9A-F]{6}$/i),
      success: z.string().regex(/^#[0-9A-F]{6}$/i),
      warning: z.string().regex(/^#[0-9A-F]{6}$/i),
    }),
    typography: z.object({
      fontFamily: z.string().default('Inter, sans-serif'),
      fontSize: z.object({
        xs: z.string().default('0.75rem'),
        sm: z.string().default('0.875rem'),
        base: z.string().default('1rem'),
        lg: z.string().default('1.125rem'),
        xl: z.string().default('1.25rem'),
      }),
      fontWeight: z.object({
        normal: z.number().default(400),
        medium: z.number().default(500),
        semibold: z.number().default(600),
        bold: z.number().default(700),
      }),
    }),
    spacing: z.object({
      xs: z.string().default('0.25rem'),
      sm: z.string().default('0.5rem'),
      md: z.string().default('1rem'),
      lg: z.string().default('1.5rem'),
      xl: z.string().default('2rem'),
    }),
    borderRadius: z.object({
      sm: z.string().default('0.25rem'),
      md: z.string().default('0.5rem'),
      lg: z.string().default('0.75rem'),
      xl: z.string().default('1rem'),
    }),
    shadows: z.object({
      sm: z.string().default('0 1px 2px 0 rgb(0 0 0 / 0.05)'),
      md: z.string().default('0 4px 6px -1px rgb(0 0 0 / 0.1)'),
      lg: z.string().default('0 10px 15px -3px rgb(0 0 0 / 0.1)'),
      xl: z.string().default('0 20px 25px -5px rgb(0 0 0 / 0.1)'),
    }),
  }),
  isDefault: z.boolean().default(false),
});

export const WidgetAuthSchema = z.object({
  widgetId: z.string(),
  permissions: z.object({
    canSendMessages: z.boolean().default(true),
    canReceiveMessages: z.boolean().default(true),
    canUploadFiles: z.boolean().default(false),
    canUseVoice: z.boolean().default(false),
    canAccessAnalytics: z.boolean().default(false),
    canModifySettings: z.boolean().default(false),
  }),
  expiresAt: z.date().optional(),
});

export const ChatMessageSchema = z.object({
  message: z.string().min(1).max(4000),
  sessionId: z.string(),
  language: z.string().default('en'),
  metadata: z.object({
    type: z.enum(['text', 'voice', 'file']).default('text'),
    fileUrl: z.string().url().optional(),
    voiceData: z.string().optional(),
    context: z.record(z.any()).optional(),
  }).optional(),
});

export const VoiceInputSchema = z.object({
  language: z.string().default('en'),
  sessionId: z.string(),
  metadata: z.object({
    duration: z.number().optional(),
    format: z.string().optional(),
    sampleRate: z.number().optional(),
  }).optional(),
});

// Type exports
export type WidgetConfigData = z.infer<typeof WidgetConfigSchema>;
export type WidgetSessionData = z.infer<typeof WidgetSessionSchema>;
export type WidgetAnalyticsEventData = z.infer<typeof WidgetAnalyticsEventSchema>;
export type WidgetThemeData = z.infer<typeof WidgetThemeSchema>;
export type WidgetAuthData = z.infer<typeof WidgetAuthSchema>;
export type ChatMessageData = z.infer<typeof ChatMessageSchema>;
export type VoiceInputData = z.infer<typeof VoiceInputSchema>;

// APIX Event Types for Widgets
export const WIDGET_EVENTS = {
  CONFIG_UPDATED: 'widget.config.updated',
  SESSION_STARTED: 'widget.session.started',
  SESSION_ENDED: 'widget.session.ended',
  MESSAGE_SENT: 'widget.message.sent',
  MESSAGE_RECEIVED: 'widget.message.received',
  VOICE_INPUT_PROCESSED: 'widget.voice.processed',
  FILE_UPLOADED: 'widget.file.uploaded',
  ANALYTICS_EVENT: 'widget.analytics.event',
  ERROR_OCCURRED: 'widget.error.occurred',
  THEME_CHANGED: 'widget.theme.changed',
  LANGUAGE_CHANGED: 'widget.language.changed',
  STATE_CHANGED: 'widget.state.changed',
} as const;

// Widget Channel Names
export const WIDGET_CHANNELS = {
  CONFIG: 'widget.config',
  SESSIONS: 'widget.sessions',
  MESSAGES: 'widget.messages',
  ANALYTICS: 'widget.analytics',
  ERRORS: 'widget.errors',
} as const;

// Error Types
export enum WidgetErrorType {
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  AUTHORIZATION_FAILED = 'AUTHORIZATION_FAILED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INVALID_DOMAIN = 'INVALID_DOMAIN',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
}

export const WidgetErrorSchema = z.object({
  type: z.nativeEnum(WidgetErrorType),
  message: z.string(),
  details: z.record(z.any()).optional(),
  widgetId: z.string().optional(),
  sessionId: z.string().optional(),
  timestamp: z.date().default(() => new Date()),
});

export type WidgetErrorData = z.infer<typeof WidgetErrorSchema>;