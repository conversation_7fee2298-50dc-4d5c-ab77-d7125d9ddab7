import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { RedisService } from '../redis/redis.service';
import { ApixService } from '../apix/apix.service';
import { 
  WidgetConfigData, 
  WidgetThemeData, 
  WidgetAnalyticsEventData,
  WIDGET_EVENTS,
  WIDGET_CHANNELS 
} from './widget.types';
import { WidgetType } from '@prisma/client';

@Injectable()
export class WidgetConfigService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly redis: RedisService,
    private readonly apix: ApixService,
  ) {}

  async createWidget(organizationId: string, data: WidgetConfigData) {
    const widget = await this.prisma.widgetConfig.create({
      data: {
        organizationId,
        name: data.name,
        type: data.type,
        settings: data.settings,
        domains: data.domains,
        isActive: data.isActive,
      },
    });

    // Cache widget config
    await this.cacheWidgetConfig(widget.id, widget);

    // Emit APIX event
    await this.apix.emit({
      type: WIDGET_EVENTS.CONFIG_UPDATED,
      channel: `${WIDGET_CHANNELS.CONFIG}.${organizationId}`,
      payload: {
        widgetId: widget.id,
        action: 'created',
        config: widget,
      },
    });

    return widget;
  }

  async getWidget(widgetId: string) {
    // Try cache first
    const cached = await this.redis.get(`widget_config:${widgetId}`);
    if (cached) {
      return JSON.parse(cached);
    }

    const widget = await this.prisma.widgetConfig.findUnique({
      where: { id: widgetId },
      include: {
        sessions: {
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
        analytics: {
          take: 100,
          orderBy: { timestamp: 'desc' },
        },
      },
    });

    if (!widget) {
      throw new NotFoundException('Widget not found');
    }

    // Cache for future requests
    await this.cacheWidgetConfig(widgetId, widget);

    return widget;
  }

  async updateWidget(widgetId: string, data: Partial<WidgetConfigData>) {
    const widget = await this.prisma.widgetConfig.update({
      where: { id: widgetId },
      data: {
        ...(data.name && { name: data.name }),
        ...(data.type && { type: data.type }),
        ...(data.settings && { settings: data.settings }),
        ...(data.domains && { domains: data.domains }),
        ...(data.isActive !== undefined && { isActive: data.isActive }),
      },
    });

    // Update cache
    await this.cacheWidgetConfig(widgetId, widget);

    // Emit APIX event
    await this.apix.emit({
      type: WIDGET_EVENTS.CONFIG_UPDATED,
      channel: `${WIDGET_CHANNELS.CONFIG}.${widget.organizationId}`,
      payload: {
        widgetId: widget.id,
        action: 'updated',
        config: widget,
        changes: data,
      },
    });

    return widget;
  }

  async deleteWidget(widgetId: string) {
    const widget = await this.prisma.widgetConfig.findUnique({
      where: { id: widgetId },
      select: { organizationId: true },
    });

    if (!widget) {
      throw new NotFoundException('Widget not found');
    }

    await this.prisma.widgetConfig.delete({
      where: { id: widgetId },
    });

    // Remove from cache
    await this.redis.del(`widget_config:${widgetId}`);

    // Emit APIX event
    await this.apix.emit({
      type: WIDGET_EVENTS.CONFIG_UPDATED,
      channel: `${WIDGET_CHANNELS.CONFIG}.${widget.organizationId}`,
      payload: {
        widgetId,
        action: 'deleted',
      },
    });

    return { success: true };
  }

  async getWidgetsByOrganization(organizationId: string) {
    const widgets = await this.prisma.widgetConfig.findMany({
      where: { organizationId },
      include: {
        _count: {
          select: {
            sessions: true,
            analytics: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return widgets;
  }

  async createTheme(organizationId: string, data: WidgetThemeData) {
    const theme = await this.prisma.widgetTheme.create({
      data: {
        organizationId,
        name: data.name,
        config: data.config,
        isDefault: data.isDefault,
      },
    });

    // If this is set as default, unset other defaults
    if (data.isDefault) {
      await this.prisma.widgetTheme.updateMany({
        where: {
          organizationId,
          id: { not: theme.id },
        },
        data: { isDefault: false },
      });
    }

    // Cache theme
    await this.redis.setex(
      `widget_theme:${theme.id}`,
      3600,
      JSON.stringify(theme),
    );

    return theme;
  }

  async getTheme(themeId: string) {
    const cached = await this.redis.get(`widget_theme:${themeId}`);
    if (cached) {
      return JSON.parse(cached);
    }

    const theme = await this.prisma.widgetTheme.findUnique({
      where: { id: themeId },
    });

    if (!theme) {
      throw new NotFoundException('Theme not found');
    }

    await this.redis.setex(
      `widget_theme:${themeId}`,
      3600,
      JSON.stringify(theme),
    );

    return theme;
  }

  async getThemesByOrganization(organizationId: string) {
    const themes = await this.prisma.widgetTheme.findMany({
      where: { organizationId },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'desc' },
      ],
    });

    return themes;
  }

  async updateTheme(themeId: string, data: Partial<WidgetThemeData>) {
    const theme = await this.prisma.widgetTheme.update({
      where: { id: themeId },
      data: {
        ...(data.name && { name: data.name }),
        ...(data.config && { config: data.config }),
        ...(data.isDefault !== undefined && { isDefault: data.isDefault }),
      },
    });

    // If this is set as default, unset other defaults
    if (data.isDefault) {
      await this.prisma.widgetTheme.updateMany({
        where: {
          organizationId: theme.organizationId,
          id: { not: theme.id },
        },
        data: { isDefault: false },
      });
    }

    // Update cache
    await this.redis.setex(
      `widget_theme:${themeId}`,
      3600,
      JSON.stringify(theme),
    );

    return theme;
  }

  async deleteTheme(themeId: string) {
    const theme = await this.prisma.widgetTheme.findUnique({
      where: { id: themeId },
    });

    if (!theme) {
      throw new NotFoundException('Theme not found');
    }

    if (theme.isDefault) {
      throw new BadRequestException('Cannot delete default theme');
    }

    await this.prisma.widgetTheme.delete({
      where: { id: themeId },
    });

    await this.redis.del(`widget_theme:${themeId}`);

    return { success: true };
  }

  async trackAnalytics(data: WidgetAnalyticsEventData) {
    const analytics = await this.prisma.widgetAnalytics.create({
      data: {
        widgetId: data.widgetId,
        organizationId: await this.getWidgetOrganizationId(data.widgetId),
        eventType: data.eventType,
        eventData: data.eventData,
        sessionId: data.sessionId,
        userId: data.userId,
        metadata: data.metadata,
      },
    });

    // Emit APIX event for real-time analytics
    await this.apix.emit({
      type: WIDGET_EVENTS.ANALYTICS_EVENT,
      channel: `${WIDGET_CHANNELS.ANALYTICS}.${analytics.organizationId}`,
      payload: analytics,
    });

    // Update real-time metrics in Redis
    await this.updateRealTimeMetrics(data.widgetId, data.eventType);

    return analytics;
  }

  async getAnalytics(widgetId: string, filters?: {
    eventType?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }) {
    const where: any = { widgetId };

    if (filters?.eventType) {
      where.eventType = filters.eventType;
    }

    if (filters?.startDate || filters?.endDate) {
      where.timestamp = {};
      if (filters.startDate) {
        where.timestamp.gte = filters.startDate;
      }
      if (filters.endDate) {
        where.timestamp.lte = filters.endDate;
      }
    }

    const analytics = await this.prisma.widgetAnalytics.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: filters?.limit || 100,
    });

    return analytics;
  }

  async getAnalyticsSummary(widgetId: string, period: 'day' | 'week' | 'month' = 'day') {
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
    }

    const analytics = await this.prisma.widgetAnalytics.groupBy({
      by: ['eventType'],
      where: {
        widgetId,
        timestamp: {
          gte: startDate,
        },
      },
      _count: {
        eventType: true,
      },
    });

    const totalEvents = analytics.reduce((sum, item) => sum + item._count.eventType, 0);

    return {
      period,
      totalEvents,
      eventBreakdown: analytics.map(item => ({
        eventType: item.eventType,
        count: item._count.eventType,
        percentage: ((item._count.eventType / totalEvents) * 100).toFixed(2),
      })),
    };
  }

  private async cacheWidgetConfig(widgetId: string, config: any) {
    await this.redis.setex(
      `widget_config:${widgetId}`,
      3600, // 1 hour
      JSON.stringify(config),
    );
  }

  private async getWidgetOrganizationId(widgetId: string): Promise<string> {
    const widget = await this.prisma.widgetConfig.findUnique({
      where: { id: widgetId },
      select: { organizationId: true },
    });

    if (!widget) {
      throw new NotFoundException('Widget not found');
    }

    return widget.organizationId;
  }

  private async updateRealTimeMetrics(widgetId: string, eventType: string) {
    const key = `widget_metrics:${widgetId}:${eventType}`;
    const today = new Date().toISOString().split('T')[0];
    const hourKey = `${key}:${today}:${new Date().getHours()}`;

    // Increment hourly counter
    await this.redis.incr(hourKey);
    await this.redis.expire(hourKey, 86400); // Expire after 24 hours

    // Increment daily counter
    const dailyKey = `${key}:${today}`;
    await this.redis.incr(dailyKey);
    await this.redis.expire(dailyKey, 86400 * 7); // Expire after 7 days
  }

  async getRealTimeMetrics(widgetId: string) {
    const today = new Date().toISOString().split('T')[0];
    const currentHour = new Date().getHours();

    const metrics: any = {};

    // Get hourly metrics for today
    for (let hour = 0; hour <= currentHour; hour++) {
      const hourKey = `widget_metrics:${widgetId}:*:${today}:${hour}`;
      const keys = await this.redis.keys(hourKey);
      
      for (const key of keys) {
        const value = await this.redis.get(key);
        const eventType = key.split(':')[2];
        
        if (!metrics[eventType]) {
          metrics[eventType] = { hourly: {}, daily: 0 };
        }
        
        metrics[eventType].hourly[hour] = parseInt(value || '0');
      }
    }

    // Get daily totals
    const dailyKeys = await this.redis.keys(`widget_metrics:${widgetId}:*:${today}`);
    for (const key of dailyKeys) {
      if (!key.includes(':' + currentHour)) continue; // Skip hourly keys
      
      const value = await this.redis.get(key);
      const eventType = key.split(':')[2];
      
      if (!metrics[eventType]) {
        metrics[eventType] = { hourly: {}, daily: 0 };
      }
      
      metrics[eventType].daily = parseInt(value || '0');
    }

    return metrics;
  }
}