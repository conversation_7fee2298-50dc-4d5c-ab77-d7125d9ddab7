import { Injectable, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../database/prisma.service';
import { RedisService } from '../redis/redis.service';
import { WidgetAuthData, WidgetErrorType } from './widget.types';
import { randomBytes, createHash } from 'crypto';

@Injectable()
export class WidgetAuthService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
    private readonly redis: RedisService,
  ) {}

  async generateApiKey(widgetId: string, organizationId: string, permissions: any): Promise<string> {
    const apiKey = `wk_${randomBytes(32).toString('hex')}`;
    const hashedKey = createHash('sha256').update(apiKey).digest('hex');

    await this.prisma.widgetAuth.create({
      data: {
        widgetId,
        organizationId,
        apiKey: hashedKey,
        permissions,
        isActive: true,
      },
    });

    // Cache the API key for faster lookups
    await this.redis.setex(
      `widget_auth:${hashedKey}`,
      3600, // 1 hour
      JSON.stringify({ widgetId, organizationId, permissions }),
    );

    return apiKey;
  }

  async validateApiKey(apiKey: string): Promise<{ widgetId: string; organizationId: string; permissions: any }> {
    const hashedKey = createHash('sha256').update(apiKey).digest('hex');

    // Check cache first
    const cached = await this.redis.get(`widget_auth:${hashedKey}`);
    if (cached) {
      return JSON.parse(cached);
    }

    // Check database
    const auth = await this.prisma.widgetAuth.findUnique({
      where: { apiKey: hashedKey },
      include: { widget: true },
    });

    if (!auth || !auth.isActive) {
      throw new UnauthorizedException('Invalid API key');
    }

    if (auth.expiresAt && auth.expiresAt < new Date()) {
      throw new UnauthorizedException('API key expired');
    }

    const result = {
      widgetId: auth.widgetId,
      organizationId: auth.organizationId,
      permissions: auth.permissions,
    };

    // Cache for future requests
    await this.redis.setex(
      `widget_auth:${hashedKey}`,
      3600,
      JSON.stringify(result),
    );

    return result;
  }

  async generateWidgetToken(widgetId: string, sessionId: string, organizationId: string): Promise<string> {
    const payload = {
      widgetId,
      sessionId,
      organizationId,
      type: 'widget_session',
      iat: Math.floor(Date.now() / 1000),
    };

    const token = this.jwtService.sign(payload, {
      expiresIn: '24h',
      issuer: 'synapseai-widget',
    });

    // Store session info in Redis
    await this.redis.setex(
      `widget_session:${sessionId}`,
      86400, // 24 hours
      JSON.stringify({
        widgetId,
        organizationId,
        token,
        createdAt: new Date().toISOString(),
      }),
    );

    return token;
  }

  async validateWidgetToken(token: string): Promise<{ widgetId: string; sessionId: string; organizationId: string }> {
    try {
      const payload = this.jwtService.verify(token, {
        issuer: 'synapseai-widget',
      });

      if (payload.type !== 'widget_session') {
        throw new UnauthorizedException('Invalid token type');
      }

      // Check if session still exists
      const sessionData = await this.redis.get(`widget_session:${payload.sessionId}`);
      if (!sessionData) {
        throw new UnauthorizedException('Session expired');
      }

      return {
        widgetId: payload.widgetId,
        sessionId: payload.sessionId,
        organizationId: payload.organizationId,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  async validateDomain(widgetId: string, domain: string): Promise<boolean> {
    const widget = await this.prisma.widgetConfig.findUnique({
      where: { id: widgetId },
      select: { domains: true, isActive: true },
    });

    if (!widget || !widget.isActive) {
      return false;
    }

    // If no domains specified, allow all
    if (!widget.domains || widget.domains.length === 0) {
      return true;
    }

    // Check if domain matches any allowed domain
    return widget.domains.some(allowedDomain => {
      // Support wildcard subdomains
      if (allowedDomain.startsWith('*.')) {
        const baseDomain = allowedDomain.substring(2);
        return domain.endsWith(baseDomain);
      }
      return domain === allowedDomain;
    });
  }

  async checkRateLimit(widgetId: string, sessionId: string, action: string): Promise<boolean> {
    const key = `rate_limit:${widgetId}:${sessionId}:${action}`;
    const current = await this.redis.get(key);
    
    // Get widget rate limit settings
    const widget = await this.prisma.widgetConfig.findUnique({
      where: { id: widgetId },
      select: { settings: true },
    });

    const rateLimit = (widget?.settings as any)?.security?.rateLimitPerMinute || 60;
    
    if (current && parseInt(current) >= rateLimit) {
      return false;
    }

    // Increment counter
    const pipeline = this.redis.multi();
    pipeline.incr(key);
    pipeline.expire(key, 60); // 1 minute window
    await pipeline.exec();

    return true;
  }

  async checkPermission(widgetId: string, permission: string): Promise<boolean> {
    const auth = await this.prisma.widgetAuth.findFirst({
      where: { widgetId, isActive: true },
      select: { permissions: true },
    });

    if (!auth) {
      return false;
    }

    const permissions = auth.permissions as any;
    return permissions[permission] === true;
  }

  async revokeApiKey(apiKey: string): Promise<void> {
    const hashedKey = createHash('sha256').update(apiKey).digest('hex');

    await this.prisma.widgetAuth.updateMany({
      where: { apiKey: hashedKey },
      data: { isActive: false },
    });

    // Remove from cache
    await this.redis.del(`widget_auth:${hashedKey}`);
  }

  async revokeWidgetSession(sessionId: string): Promise<void> {
    await this.redis.del(`widget_session:${sessionId}`);
  }

  async cleanupExpiredSessions(): Promise<void> {
    // This would typically be called by a cron job
    const expiredSessions = await this.prisma.widgetSession.findMany({
      where: {
        createdAt: {
          lt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24 hours ago
        },
      },
      select: { sessionId: true },
    });

    for (const session of expiredSessions) {
      await this.redis.del(`widget_session:${session.sessionId}`);
    }

    // Clean up database records
    await this.prisma.widgetSession.deleteMany({
      where: {
        createdAt: {
          lt: new Date(Date.now() - 24 * 60 * 60 * 1000),
        },
      },
    });
  }

  async getSessionInfo(sessionId: string): Promise<any> {
    const sessionData = await this.redis.get(`widget_session:${sessionId}`);
    return sessionData ? JSON.parse(sessionData) : null;
  }

  async updateSessionMetadata(sessionId: string, metadata: any): Promise<void> {
    const sessionData = await this.getSessionInfo(sessionId);
    if (sessionData) {
      sessionData.metadata = { ...sessionData.metadata, ...metadata };
      await this.redis.setex(
        `widget_session:${sessionId}`,
        86400,
        JSON.stringify(sessionData),
      );
    }
  }
}