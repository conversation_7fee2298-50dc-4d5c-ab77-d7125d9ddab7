import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, UseGuards } from '@nestjs/common';
import { AuthService } from '../auth/auth.service';
import { RedisService } from '../redis/redis.service';

@Injectable()
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  transports: ['websocket', 'polling'],
})
export class WebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private connectedClients = new Map<string, Socket>();

  constructor(
    private authService: AuthService,
    private redisService: RedisService,
  ) {}

  async handleConnection(client: Socket) {
    try {
      const token = client.handshake.auth.token || client.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        client.disconnect();
        return;
      }

      const user = await this.authService.validateToken(token);
      
      if (!user) {
        client.disconnect();
        return;
      }

      client.data.user = user;
      client.data.organizationId = user.organizationId;
      
      // Join organization room
      client.join(`org:${user.organizationId}`);
      client.join(`user:${user.id}`);
      
      this.connectedClients.set(client.id, client);
      
      // Store connection in Redis
      await this.redisService.hset(
        `ws:connections:${user.organizationId}`,
        user.id,
        JSON.stringify({
          socketId: client.id,
          userId: user.id,
          connectedAt: new Date().toISOString(),
        }),
      );

      console.log(`Client connected: ${user.email} (${client.id})`);
      
      // Notify organization about new connection
      this.server.to(`org:${user.organizationId}`).emit('user:online', {
        userId: user.id,
        email: user.email,
      });
      
    } catch (error) {
      console.error('WebSocket connection error:', error);
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    try {
      const user = client.data.user;
      
      if (user) {
        // Remove from Redis
        await this.redisService.hset(
          `ws:connections:${user.organizationId}`,
          user.id,
          '',
        );
        
        // Notify organization about disconnection
        this.server.to(`org:${user.organizationId}`).emit('user:offline', {
          userId: user.id,
          email: user.email,
        });
        
        console.log(`Client disconnected: ${user.email} (${client.id})`);
      }
      
      this.connectedClients.delete(client.id);
    } catch (error) {
      console.error('WebSocket disconnection error:', error);
    }
  }

  @SubscribeMessage('join:room')
  handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { room: string },
  ) {
    client.join(data.room);
    client.emit('joined:room', { room: data.room });
  }

  @SubscribeMessage('leave:room')
  handleLeaveRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { room: string },
  ) {
    client.leave(data.room);
    client.emit('left:room', { room: data.room });
  }

  @SubscribeMessage('message')
  handleMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { room: string; message: string },
  ) {
    const user = client.data.user;
    
    this.server.to(data.room).emit('message', {
      id: Date.now().toString(),
      message: data.message,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      timestamp: new Date().toISOString(),
    });
  }

  // Utility methods for sending messages
  sendToUser(userId: string, event: string, data: any) {
    this.server.to(`user:${userId}`).emit(event, data);
  }

  sendToOrganization(organizationId: string, event: string, data: any) {
    this.server.to(`org:${organizationId}`).emit(event, data);
  }

  sendToRoom(room: string, event: string, data: any) {
    this.server.to(room).emit(event, data);
  }
}