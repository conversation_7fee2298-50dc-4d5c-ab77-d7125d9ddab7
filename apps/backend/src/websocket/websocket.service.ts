import { Injectable } from '@nestjs/common';
import { WebSocketGateway } from './websocket.gateway';

@Injectable()
export class WebSocketService {
  constructor(private websocketGateway: WebSocketGateway) {}

  sendToUser(userId: string, event: string, data: any) {
    this.websocketGateway.sendToUser(userId, event, data);
  }

  sendToOrganization(organizationId: string, event: string, data: any) {
    this.websocketGateway.sendToOrganization(organizationId, event, data);
  }

  sendToRoom(room: string, event: string, data: any) {
    this.websocketGateway.sendToRoom(room, event, data);
  }

  broadcast(event: string, data: any) {
    this.websocketGateway.server.emit(event, data);
  }
}