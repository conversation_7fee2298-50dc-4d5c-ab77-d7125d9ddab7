import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT_SECRET'),
      algorithms: ['RS256'],
    });
  }

  async validate(payload: any) {
    const user = await this.authService.validateToken(payload.sub);
    return {
      id: payload.sub,
      email: payload.email,
      organizationId: payload.organizationId,
      organizationSlug: payload.organizationSlug,
      roles: payload.roles,
      user,
    };
  }
}