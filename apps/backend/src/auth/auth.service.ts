import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../database/prisma.service';
import { UsersService } from '../users/users.service';
import { RedisService } from '../redis/redis.service';
import * as bcrypt from 'bcrypt';
import { z } from 'zod';

const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  organizationSlug: z.string().optional(),
});

const RegisterSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  organizationName: z.string().min(1),
});

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private usersService: UsersService,
    private redisService: RedisService,
    private configService: ConfigService,
  ) {}

  async validateUser(email: string, password: string, organizationSlug?: string) {
    const user = await this.prisma.user.findUnique({
      where: { email },
      include: {
        organization: true,
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user || !user.isActive) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (organizationSlug && user.organization.slug !== organizationSlug) {
      throw new UnauthorizedException('Invalid organization');
    }

    const isPasswordValid = await bcrypt.compare(password, password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    return user;
  }

  async login(loginDto: z.infer<typeof LoginSchema>) {
    const validatedData = LoginSchema.parse(loginDto);
    
    const user = await this.validateUser(
      validatedData.email,
      validatedData.password,
      validatedData.organizationSlug,
    );

    const payload = {
      sub: user.id,
      email: user.email,
      organizationId: user.organizationId,
      organizationSlug: user.organization.slug,
      roles: user.userRoles.map(ur => ur.role.name),
    };

    const accessToken = this.jwtService.sign(payload);
    
    // Store session in Redis
    const sessionId = `session:${user.id}:${Date.now()}`;
    await this.redisService.setex(sessionId, 86400, JSON.stringify({
      userId: user.id,
      organizationId: user.organizationId,
      token: accessToken,
    }));

    // Update last login
    await this.prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    return {
      accessToken,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        organization: user.organization,
        roles: user.userRoles.map(ur => ur.role),
      },
    };
  }

  async register(registerDto: z.infer<typeof RegisterSchema>) {
    const validatedData = RegisterSchema.parse(registerDto);
    
    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      throw new UnauthorizedException('User already exists');
    }

    // Create organization and user in transaction
    const result = await this.prisma.$transaction(async (tx) => {
      // Create organization
      const organization = await tx.organization.create({
        data: {
          name: validatedData.organizationName,
          slug: validatedData.organizationName.toLowerCase().replace(/\s+/g, '-'),
        },
      });

      // Create admin role
      const adminRole = await tx.role.create({
        data: {
          name: 'admin',
          description: 'Full system access',
          permissions: { all: true },
          isSystem: true,
          organizationId: organization.id,
        },
      });

      // Hash password
      const hashedPassword = await bcrypt.hash(validatedData.password, 12);

      // Create user
      const user = await tx.user.create({
        data: {
          email: validatedData.email,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          organizationId: organization.id,
        },
        include: {
          organization: true,
        },
      });

      // Assign admin role
      await tx.userRole.create({
        data: {
          userId: user.id,
          roleId: adminRole.id,
        },
      });

      return { user, organization, role: adminRole };
    });

    return this.login({
      email: validatedData.email,
      password: validatedData.password,
    });
  }

  async logout(userId: string) {
    const sessionKeys = await this.redisService.keys(`session:${userId}:*`);
    if (sessionKeys.length > 0) {
      await this.redisService.del(...sessionKeys);
    }
  }

  async validateToken(token: string) {
    try {
      const payload = this.jwtService.verify(token);
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        include: {
          organization: true,
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      if (!user || !user.isActive) {
        throw new UnauthorizedException('Invalid token');
      }

      return user;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }
}