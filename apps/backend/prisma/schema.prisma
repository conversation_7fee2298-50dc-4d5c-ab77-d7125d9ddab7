// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id             String   @id @default(cuid())
  email          String   @unique
  password       String
  name           String?
  organizationId String?
  role           String   @default("user")
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  connections    ApiXConnection[]
  events         ApiXEvent[]
  auditLogs      AuditLog[]

  @@map("users")
}

model ApiXConnection {
  id             String   @id @default(cuid())
  sessionId      String   @unique
  organizationId String
  userId         String?
  clientType     String
  channels       String[]
  metadata       Json?
  connectedAt    DateTime
  lastHeartbeat  DateTime
  status         String
  ipAddress      String?
  userAgent      String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  user           User?              @relation(fields: [userId], references: [id])
  subscriptions  ApiXSubscription[]

  @@map("apix_connections")
}

model ApiXEvent {
  id             String    @id @default(cuid())
  eventType      String
  channel        String
  payload        Json
  sessionId      String?
  organizationId String?
  userId         String?
  acknowledgment Boolean   @default(false)
  retryCount     Int       @default(0)
  maxRetries     Int       @default(3)
  status         String
  processedAt    DateTime?
  failedAt       DateTime?
  error          String?
  createdAt      DateTime  @default(now())
  metadata       Json?
  correlationId  String?

  // Relations
  user           User?     @relation(fields: [userId], references: [id])

  @@map("apix_events")
}

model ApiXChannel {
  id             String   @id @default(cuid())
  name           String   @unique
  type           String
  organizationId String?
  permissions    Json
  subscribers    Int      @default(0)
  isActive       Boolean  @default(true)
  metadata       Json?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  subscriptions  ApiXSubscription[]

  @@map("apix_channels")
}

model ApiXSubscription {
  id           String   @id @default(cuid())
  connectionId String
  channelName  String
  filters      Json?
  permissions  Json?
  createdAt    DateTime @default(now())

  // Relations
  connection   ApiXConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  channel      ApiXChannel    @relation(fields: [channelName], references: [name])

  @@unique([connectionId, channelName])
  @@map("apix_subscriptions")
}

model ApiXMetrics {
  id             String   @id @default(cuid())
  connectionId   String
  eventType      String
  channel        String
  latency        Float
  throughput     Int
  errorCount     Int
  timestamp      DateTime
  organizationId String?

  @@map("apix_metrics")
}

model QueueJob {
  id           String    @id @default(cuid())
  name         String
  data         Json
  status       String
  attempts     Int       @default(0)
  maxAttempts  Int       @default(3)
  processedAt  DateTime?
  failedAt     DateTime?
  error        String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  @@map("queue_jobs")
}

model AuditLog {
  id             String   @id @default(cuid())
  eventType      String
  action         String
  entityType     String
  entityId       String
  userId         String?
  organizationId String?
  sessionId      String?
  ipAddress      String?
  userAgent      String?
  metadata       Json?
  timestamp      DateTime @default(now())

  // Relations
  user           User?    @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

model SessionStore {
  id             String   @id @default(cuid())
  sessionId      String   @unique
  organizationId String
  userId         String?
  data           Json
  metadata       Json?
  size           Int      @default(0)
  lastAccessed   DateTime @default(now())
  expiresAt      DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("session_store")
}

model NotificationTemplate {
  id             String   @id @default(cuid())
  name           String   @unique
  type           String
  organizationId String?
  template       Json
  isActive       Boolean  @default(true)
  metadata       Json?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("notification_templates")
}

model NotificationDelivery {
  id             String    @id @default(cuid())
  templateId     String
  recipientId    String
  recipientType  String
  channel        String
  payload        Json
  status         String
  attempts       Int       @default(0)
  deliveredAt    DateTime?
  failedAt       DateTime?
  error          String?
  organizationId String?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  @@map("notification_deliveries")
}

// UAUI Frontend SDK Models
model WidgetConfig {
  id             String     @id @default(cuid())
  organizationId String
  name           String
  type           WidgetType
  settings       Json       // Theme, features, etc.
  isActive       Boolean    @default(true)
  domains        String[]   // Allowed embedding domains
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt

  // Relations
  sessions       WidgetSession[]
  analytics      WidgetAnalytics[]

  @@map("widget_configs")
}

model WidgetSession {
  id         String   @id @default(cuid())
  widgetId   String
  sessionId  String
  metadata   Json
  createdAt  DateTime @default(now())

  // Relations
  widget     WidgetConfig @relation(fields: [widgetId], references: [id], onDelete: Cascade)

  @@map("widget_sessions")
}

model WidgetAnalytics {
  id             String   @id @default(cuid())
  widgetId       String
  organizationId String
  eventType      String
  eventData      Json
  timestamp      DateTime @default(now())
  sessionId      String?
  userId         String?
  metadata       Json?

  // Relations
  widget         WidgetConfig @relation(fields: [widgetId], references: [id], onDelete: Cascade)

  @@map("widget_analytics")
}

model WidgetTheme {
  id             String   @id @default(cuid())
  organizationId String
  name           String
  config         Json     // Theme configuration
  isDefault      Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([organizationId, name])
  @@map("widget_themes")
}

model WidgetAuth {
  id             String   @id @default(cuid())
  widgetId       String
  organizationId String
  apiKey         String   @unique
  permissions    Json
  isActive       Boolean  @default(true)
  expiresAt      DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("widget_auth")
}

enum WidgetType {
  FLOATING_ASSISTANT
  CHAT_PANEL
  WORKFLOW_TRIGGER
  AGENT_EMBED
  AGENT_WIZARD
  TOOL_PANEL
  WORKFLOW_BUILDER
  AUTO_FORM
}