'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Bot, 
  Settings, 
  MessageCircle,
  Zap,
  Brain,
  Activity,
  X,
  ExternalLink,
} from 'lucide-react';
import { WidgetSDK } from '@/lib/widget-sdk';
import { WidgetConfig } from '@/types/widget.types';

interface AgentEmbedProps {
  config: WidgetConfig;
  agentId: string;
  agentName: string;
  agentDescription?: string;
  capabilities?: string[];
  showHeader?: boolean;
  showStatus?: boolean;
  showCapabilities?: boolean;
  height?: number;
  className?: string;
  onInteraction?: (type: string, data: any) => void;
  onError?: (error: any) => void;
}

interface AgentStatus {
  status: 'online' | 'offline' | 'busy' | 'idle';
  lastActive: Date;
  activeConversations: number;
  totalInteractions: number;
}

export const AgentEmbed: React.FC<AgentEmbedProps> = ({
  config,
  agentId,
  agentName,
  agentDescription,
  capabilities = [],
  showHeader = true,
  showStatus = true,
  showCapabilities = true,
  height = 400,
  className = '',
  onInteraction,
  onError,
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [agentStatus, setAgentStatus] = useState<AgentStatus>({
    status: 'offline',
    lastActive: new Date(),
    activeConversations: 0,
    totalInteractions: 0,
  });
  const [isInteracting, setIsInteracting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sdkRef = React.useRef<WidgetSDK | null>(null);
  const theme = config.settings.theme;

  // Initialize SDK and agent status
  useEffect(() => {
    const initializeAgent = async () => {
      try {
        sdkRef.current = new WidgetSDK(config);
        
        sdkRef.current.on('initialized', () => {
          setIsInitialized(true);
          setError(null);
          
          // Simulate agent status (in production, this would come from your agent service)
          setAgentStatus({
            status: 'online',
            lastActive: new Date(),
            activeConversations: Math.floor(Math.random() * 5),
            totalInteractions: Math.floor(Math.random() * 1000) + 100,
          });
        });

        sdkRef.current.on('error', (error: any) => {
          setError(error.message);
          onError?.(error);
        });

        await sdkRef.current.initialize();
        
        // Track agent embed load
        await sdkRef.current.trackEvent('agent_embed_loaded', {
          agentId,
          agentName,
        });

      } catch (error: any) {
        setError(error.message);
        onError?.(error);
      }
    };

    initializeAgent();

    return () => {
      if (sdkRef.current) {
        sdkRef.current.destroy();
      }
    };
  }, [config, agentId, agentName]);

  // Simulate periodic status updates
  useEffect(() => {
    if (!isInitialized) return;

    const interval = setInterval(() => {
      setAgentStatus(prev => ({
        ...prev,
        lastActive: new Date(),
        activeConversations: Math.max(0, prev.activeConversations + (Math.random() > 0.5 ? 1 : -1)),
      }));
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [isInitialized]);

  const handleStartChat = async () => {
    if (!sdkRef.current) return;

    try {
      setIsInteracting(true);
      
      await sdkRef.current.trackEvent('agent_chat_started', {
        agentId,
        agentName,
      });

      onInteraction?.('chat_started', { agentId, agentName });
      
      // In a real implementation, this would open a chat interface
      // For now, we'll simulate the interaction
      setTimeout(() => {
        setIsInteracting(false);
        onInteraction?.('chat_completed', { 
          agentId, 
          agentName,
          duration: 5000,
        });
      }, 5000);

    } catch (error: any) {
      setError(error.message);
      setIsInteracting(false);
    }
  };

  const handleRunWorkflow = async () => {
    if (!sdkRef.current) return;

    try {
      setIsInteracting(true);
      
      await sdkRef.current.trackEvent('agent_workflow_triggered', {
        agentId,
        agentName,
      });

      onInteraction?.('workflow_started', { agentId, agentName });
      
      // Simulate workflow execution
      setTimeout(() => {
        setIsInteracting(false);
        onInteraction?.('workflow_completed', { 
          agentId, 
          agentName,
          result: 'success',
        });
      }, 3000);

    } catch (error: any) {
      setError(error.message);
      setIsInteracting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'busy': return 'bg-yellow-500';
      case 'idle': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online': return 'Online';
      case 'busy': return 'Busy';
      case 'idle': return 'Idle';
      default: return 'Offline';
    }
  };

  return (
    <Card 
      className={`bg-white dark:bg-gray-900 ${className}`}
      style={{ height }}
    >
      {/* Header */}
      {showHeader && (
        <>
          <div 
            className="p-4 border-b"
            style={{ backgroundColor: `${theme.primaryColor}10` }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div 
                  className="w-10 h-10 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: theme.primaryColor }}
                >
                  <Bot className="w-5 h-5 text-white" />
                </div>
                
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                    {agentName}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    AI Agent • {agentId}
                  </p>
                </div>
              </div>

              {showStatus && (
                <div className="flex items-center space-x-2">
                  <div 
                    className={`w-2 h-2 rounded-full ${getStatusColor(agentStatus.status)}`}
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {getStatusText(agentStatus.status)}
                  </span>
                </div>
              )}
            </div>

            {agentDescription && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                {agentDescription}
              </p>
            )}
          </div>
        </>
      )}

      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border-b border-red-200">
          <div className="flex items-center justify-between">
            <span className="text-sm text-red-700">{error}</span>
            <Button
              size="sm"
              variant="ghost"
              className="text-red-500 hover:bg-red-100 p-1"
              onClick={() => setError(null)}
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="p-4 space-y-4">
        {/* Status Information */}
        {showStatus && isInitialized && (
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {agentStatus.activeConversations}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Active Chats
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {agentStatus.totalInteractions}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Total Interactions
              </div>
            </div>
          </div>
        )}

        {/* Capabilities */}
        {showCapabilities && capabilities.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Capabilities
            </h4>
            <div className="flex flex-wrap gap-2">
              {capabilities.map((capability, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {capability}
                </Badge>
              ))}
            </div>
          </div>
        )}

        <Separator />

        {/* Action Buttons */}
        <div className="space-y-2">
          <Button
            onClick={handleStartChat}
            disabled={!isInitialized || isInteracting || agentStatus.status === 'offline'}
            className="w-full"
            style={{ backgroundColor: theme.primaryColor }}
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            {isInteracting ? 'Starting Chat...' : 'Start Chat'}
          </Button>

          <Button
            variant="outline"
            onClick={handleRunWorkflow}
            disabled={!isInitialized || isInteracting || agentStatus.status === 'offline'}
            className="w-full"
          >
            <Zap className="w-4 h-4 mr-2" />
            {isInteracting ? 'Running...' : 'Run Workflow'}
          </Button>
        </div>

        {/* Additional Info */}
        <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
          <div className="flex items-center justify-between">
            <span>Last Active:</span>
            <span>{agentStatus.lastActive.toLocaleTimeString()}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span>Response Time:</span>
            <span>~2.3s avg</span>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-3 border-t bg-gray-50 dark:bg-gray-800/50">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-1">
            <Activity className="w-3 h-3" />
            <span>Powered by SynapseAI</span>
          </div>
          
          <Button
            size="sm"
            variant="ghost"
            className="p-1 h-auto"
            onClick={() => onInteraction?.('settings_opened', { agentId })}
          >
            <Settings className="w-3 h-3" />
          </Button>
        </div>
      </div>
    </Card>
  );
};