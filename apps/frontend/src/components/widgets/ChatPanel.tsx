'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  MessageCircle, 
  Send, 
  Mic, 
  MicOff, 
  Paperclip,
  Loader2,
  AlertCircle,
  X,
} from 'lucide-react';
import { WidgetSDK } from '@/lib/widget-sdk';
import { WidgetConfig, Message } from '@/types/widget.types';

interface ChatPanelProps {
  config: WidgetConfig;
  className?: string;
  height?: number;
  onMessage?: (message: Message) => void;
  onError?: (error: any) => void;
}

export const ChatPanel: React.FC<ChatPanelProps> = ({
  config,
  className = '',
  height = 500,
  onMessage,
  onError,
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  
  const sdkRef = useRef<WidgetSDK | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);

  const theme = config.settings.theme;
  const features = config.settings.features;
  const behavior = config.settings.behavior;

  // Initialize SDK
  useEffect(() => {
    const initializeSDK = async () => {
      try {
        setIsLoading(true);
        setConnectionStatus('connecting');
        
        sdkRef.current = new WidgetSDK(config);
        
        // Set up event listeners
        sdkRef.current.on('initialized', () => {
          setIsInitialized(true);
          setConnectionStatus('connected');
          setError(null);
          
          // Add greeting message
          if (behavior.greeting) {
            const greetingMessage: Message = {
              id: 'greeting',
              content: behavior.greeting,
              sender: 'agent',
              timestamp: new Date(),
              type: 'text',
            };
            setMessages([greetingMessage]);
          }
        });

        sdkRef.current.on('connected', () => {
          setConnectionStatus('connected');
        });

        sdkRef.current.on('disconnected', () => {
          setConnectionStatus('disconnected');
        });

        sdkRef.current.on('messageReceived', (data: any) => {
          const agentMessage: Message = {
            id: Date.now().toString() + '_agent',
            content: data.response,
            sender: 'agent',
            timestamp: new Date(),
            type: 'text',
            metadata: data.metadata,
          };
          
          setMessages(prev => [...prev, agentMessage]);
          onMessage?.(agentMessage);
          setIsTyping(false);
        });

        sdkRef.current.on('error', (error: any) => {
          setError(error.message);
          setConnectionStatus('disconnected');
          onError?.(error);
        });

        sdkRef.current.on('voiceProcessed', (result: any) => {
          setInputValue(result.transcript);
        });

        await sdkRef.current.initialize();
        
      } catch (error: any) {
        setError(error.message);
        setConnectionStatus('disconnected');
        onError?.(error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeSDK();

    return () => {
      if (sdkRef.current) {
        sdkRef.current.destroy();
      }
    };
  }, [config]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !sdkRef.current || !isInitialized) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
      type: 'text',
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    onMessage?.(userMessage);

    // Show typing indicator
    if (behavior.typingIndicator) {
      setIsTyping(true);
    }

    try {
      await sdkRef.current.sendMessage(inputValue, {
        language: currentLanguage,
      });
    } catch (error: any) {
      setError(error.message);
      setIsTyping(false);
      
      const errorMessage: Message = {
        id: Date.now().toString() + '_error',
        content: 'Sorry, I encountered an error. Please try again.',
        sender: 'agent',
        timestamp: new Date(),
        type: 'text',
      };

      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const startVoiceRecording = async () => {
    if (!features.voiceInput || !sdkRef.current) return;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      const audioChunks: BlobPart[] = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
        
        try {
          await sdkRef.current!.processVoiceInput(audioBlob, {
            language: currentLanguage,
          });
        } catch (error: any) {
          setError(error.message);
        }
        
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);

    } catch (error: any) {
      setError('Failed to access microphone');
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return (
    <Card 
      className={`flex flex-col bg-white dark:bg-gray-900 ${className}`}
      style={{ height }}
    >
      {/* Header */}
      <div 
        className="flex items-center justify-between p-4 border-b"
        style={{ backgroundColor: theme.primaryColor }}
      >
        <div className="flex items-center space-x-2">
          <MessageCircle className="w-5 h-5 text-white" />
          <span className="text-white font-medium">AI Chat</span>
          
          <Badge 
            variant={connectionStatus === 'connected' ? 'default' : 'destructive'}
            className="text-xs"
          >
            {connectionStatus}
          </Badge>
        </div>
        
        {features.multiLanguage && (
          <select
            value={currentLanguage}
            onChange={(e) => setCurrentLanguage(e.target.value)}
            className="bg-transparent text-white text-sm border-none outline-none"
          >
            <option value="en">🇺🇸 EN</option>
            <option value="es">🇪🇸 ES</option>
            <option value="fr">🇫🇷 FR</option>
            <option value="de">🇩🇪 DE</option>
            <option value="zh">🇨🇳 ZH</option>
          </select>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-2 bg-red-50 border-b border-red-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4 text-red-500" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
            <Button
              size="sm"
              variant="ghost"
              className="text-red-500 hover:bg-red-100 p-1"
              onClick={clearError}
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-gray-500">Initializing chat...</p>
          </div>
        </div>
      )}

      {/* Messages */}
      {!isLoading && (
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] p-3 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
                  }`}
                  style={{
                    backgroundColor: message.sender === 'user' ? theme.primaryColor : undefined,
                    borderRadius: theme.borderRadius / 2,
                  }}
                >
                  <p className="text-sm">{message.content}</p>
                  <span className="text-xs opacity-70 mt-1 block">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      )}

      {/* Input */}
      {!isLoading && (
        <div className="p-4 border-t">
          <div className="flex items-center space-x-2">
            <div className="flex-1 relative">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={behavior.placeholder}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                className="pr-20"
                disabled={!isInitialized || connectionStatus !== 'connected'}
              />
              
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                {features.fileUpload && (
                  <Button size="sm" variant="ghost" className="p-1">
                    <Paperclip className="w-4 h-4" />
                  </Button>
                )}
                
                {features.voiceInput && (
                  <Button
                    size="sm"
                    variant="ghost"
                    className={`p-1 ${isRecording ? 'text-red-500' : ''}`}
                    onMouseDown={startVoiceRecording}
                    onMouseUp={stopVoiceRecording}
                    onMouseLeave={stopVoiceRecording}
                    disabled={!isInitialized || connectionStatus !== 'connected'}
                  >
                    {isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                  </Button>
                )}
              </div>
            </div>
            
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || !isInitialized || connectionStatus !== 'connected'}
              style={{ backgroundColor: theme.primaryColor }}
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </Card>
  );
};