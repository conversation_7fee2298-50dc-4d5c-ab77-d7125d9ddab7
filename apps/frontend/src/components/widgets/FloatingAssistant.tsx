'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  MessageCircle, 
  X, 
  Minimize2, 
  Maximize2, 
  Mic, 
  MicOff, 
  Send, 
  Paperclip,
  Settings,
  Globe,
  Volume2,
  VolumeX
} from 'lucide-react';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
  type: 'text' | 'voice' | 'file';
  metadata?: any;
}

interface FloatingAssistantProps {
  widgetId: string;
  apiUrl?: string;
  settings?: {
    theme?: {
      primaryColor?: string;
      position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
      size?: 'small' | 'medium' | 'large';
      borderRadius?: number;
      shadow?: boolean;
      animation?: boolean;
    };
    features?: {
      voiceInput?: boolean;
      fileUpload?: boolean;
      multiLanguage?: boolean;
      darkMode?: boolean;
      minimizable?: boolean;
      draggable?: boolean;
      resizable?: boolean;
    };
    behavior?: {
      autoOpen?: boolean;
      greeting?: string;
      placeholder?: string;
      maxMessages?: number;
      typingIndicator?: boolean;
      readReceipts?: boolean;
    };
  };
  onMessage?: (message: Message) => void;
  onStateChange?: (state: 'open' | 'closed' | 'minimized') => void;
}

export const FloatingAssistant: React.FC<FloatingAssistantProps> = ({
  widgetId,
  apiUrl = '/api',
  settings = {},
  onMessage,
  onStateChange,
}) => {
  const [isOpen, setIsOpen] = useState(settings.behavior?.autoOpen || false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const speechSynthesisRef = useRef<SpeechSynthesisUtterance | null>(null);
  const widgetRef = useRef<HTMLDivElement>(null);
  const dragRef = useRef({ startX: 0, startY: 0, startPosX: 0, startPosY: 0 });

  const theme = {
    primaryColor: '#3b82f6',
    position: 'bottom-right',
    size: 'medium',
    borderRadius: 12,
    shadow: true,
    animation: true,
    ...settings.theme,
  };

  const features = {
    voiceInput: true,
    fileUpload: true,
    multiLanguage: true,
    darkMode: false,
    minimizable: true,
    draggable: true,
    resizable: false,
    ...settings.features,
  };

  const behavior = {
    autoOpen: false,
    greeting: 'Hello! How can I help you today?',
    placeholder: 'Type your message...',
    maxMessages: 100,
    typingIndicator: true,
    readReceipts: true,
    ...settings.behavior,
  };

  // Initialize position based on theme
  useEffect(() => {
    const getInitialPosition = () => {
      const margin = 20;
      const widgetSize = theme.size === 'small' ? 300 : theme.size === 'large' ? 500 : 400;
      
      switch (theme.position) {
        case 'bottom-right':
          return { x: window.innerWidth - widgetSize - margin, y: window.innerHeight - 600 - margin };
        case 'bottom-left':
          return { x: margin, y: window.innerHeight - 600 - margin };
        case 'top-right':
          return { x: window.innerWidth - widgetSize - margin, y: margin };
        case 'top-left':
          return { x: margin, y: margin };
        default:
          return { x: window.innerWidth - widgetSize - margin, y: window.innerHeight - 600 - margin };
      }
    };

    setPosition(getInitialPosition());
  }, [theme.position, theme.size]);

  // Add greeting message on first open
  useEffect(() => {
    if (isOpen && messages.length === 0 && behavior.greeting) {
      const greetingMessage: Message = {
        id: 'greeting',
        content: behavior.greeting,
        sender: 'agent',
        timestamp: new Date(),
        type: 'text',
      };
      setMessages([greetingMessage]);
    }
  }, [isOpen, messages.length, behavior.greeting]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle state changes
  useEffect(() => {
    const state = isOpen ? (isMinimized ? 'minimized' : 'open') : 'closed';
    onStateChange?.(state);
  }, [isOpen, isMinimized, onStateChange]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
      type: 'text',
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    onMessage?.(userMessage);

    // Show typing indicator
    if (behavior.typingIndicator) {
      setIsTyping(true);
    }

    try {
      // Send to API
      const response = await fetch(`${apiUrl}/widgets/${widgetId}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputValue,
          sessionId: `session_${Date.now()}`,
          language: currentLanguage,
        }),
      });

      const data = await response.json();
      
      const agentMessage: Message = {
        id: Date.now().toString() + '_agent',
        content: data.response || 'I apologize, but I encountered an error. Please try again.',
        sender: 'agent',
        timestamp: new Date(),
        type: 'text',
        metadata: data.metadata,
      };

      setMessages(prev => [...prev, agentMessage]);
      onMessage?.(agentMessage);

      // Text-to-speech if enabled
      if (features.voiceInput && !isSpeaking) {
        speakMessage(agentMessage.content);
      }

    } catch (error) {
      console.error('Failed to send message:', error);
      
      const errorMessage: Message = {
        id: Date.now().toString() + '_error',
        content: 'Sorry, I encountered an error. Please try again.',
        sender: 'agent',
        timestamp: new Date(),
        type: 'text',
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const startVoiceRecording = async () => {
    if (!features.voiceInput) return;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      const audioChunks: BlobPart[] = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
        await processVoiceInput(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);

    } catch (error) {
      console.error('Failed to start voice recording:', error);
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const processVoiceInput = async (audioBlob: Blob) => {
    try {
      const formData = new FormData();
      formData.append('audio', audioBlob);
      formData.append('language', currentLanguage);

      const response = await fetch(`${apiUrl}/widgets/${widgetId}/voice`, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      
      if (data.transcript) {
        setInputValue(data.transcript);
      }

    } catch (error) {
      console.error('Failed to process voice input:', error);
    }
  };

  const speakMessage = (text: string) => {
    if (!('speechSynthesis' in window)) return;

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = currentLanguage;
    utterance.onstart = () => setIsSpeaking(true);
    utterance.onend = () => setIsSpeaking(false);
    
    speechSynthesisRef.current = utterance;
    speechSynthesis.speak(utterance);
  };

  const stopSpeaking = () => {
    if (speechSynthesis.speaking) {
      speechSynthesis.cancel();
      setIsSpeaking(false);
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!features.draggable) return;
    
    setIsDragging(true);
    dragRef.current = {
      startX: e.clientX,
      startY: e.clientY,
      startPosX: position.x,
      startPosY: position.y,
    };
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !features.draggable) return;

    const deltaX = e.clientX - dragRef.current.startX;
    const deltaY = e.clientY - dragRef.current.startY;

    setPosition({
      x: Math.max(0, Math.min(window.innerWidth - 400, dragRef.current.startPosX + deltaX)),
      y: Math.max(0, Math.min(window.innerHeight - 600, dragRef.current.startPosY + deltaY)),
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging]);

  const getWidgetSize = () => {
    switch (theme.size) {
      case 'small': return { width: 300, height: 400 };
      case 'large': return { width: 500, height: 700 };
      default: return { width: 400, height: 600 };
    }
  };

  const widgetSize = getWidgetSize();

  if (!isOpen) {
    return (
      <div
        className="fixed z-50 cursor-pointer"
        style={{
          left: position.x + widgetSize.width - 60,
          top: position.y + widgetSize.height - 60,
        }}
        onClick={() => setIsOpen(true)}
      >
        <Button
          size="lg"
          className="rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-all duration-300"
          style={{
            backgroundColor: theme.primaryColor,
            borderRadius: theme.borderRadius,
          }}
        >
          <MessageCircle className="w-6 h-6" />
        </Button>
      </div>
    );
  }

  return (
    <div
      ref={widgetRef}
      className={`fixed z-50 ${theme.shadow ? 'shadow-2xl' : ''} ${theme.animation ? 'transition-all duration-300' : ''}`}
      style={{
        left: position.x,
        top: position.y,
        width: widgetSize.width,
        height: isMinimized ? 60 : widgetSize.height,
        borderRadius: theme.borderRadius,
      }}
    >
      <Card className="h-full flex flex-col bg-white dark:bg-gray-900">
        {/* Header */}
        <div
          className="flex items-center justify-between p-4 border-b cursor-move"
          style={{ backgroundColor: theme.primaryColor }}
          onMouseDown={handleMouseDown}
        >
          <div className="flex items-center space-x-2">
            <MessageCircle className="w-5 h-5 text-white" />
            <span className="text-white font-medium">AI Assistant</span>
            {features.multiLanguage && (
              <select
                value={currentLanguage}
                onChange={(e) => setCurrentLanguage(e.target.value)}
                className="bg-transparent text-white text-sm border-none outline-none"
                onClick={(e) => e.stopPropagation()}
              >
                <option value="en">🇺🇸 EN</option>
                <option value="es">🇪🇸 ES</option>
                <option value="fr">🇫🇷 FR</option>
                <option value="de">🇩🇪 DE</option>
                <option value="zh">🇨🇳 ZH</option>
              </select>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {isSpeaking && (
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/20 p-1"
                onClick={stopSpeaking}
              >
                <VolumeX className="w-4 h-4" />
              </Button>
            )}
            
            {features.minimizable && (
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/20 p-1"
                onClick={() => setIsMinimized(!isMinimized)}
              >
                {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
              </Button>
            )}
            
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20 p-1"
              onClick={() => setIsOpen(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        message.sender === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
                      }`}
                      style={{
                        backgroundColor: message.sender === 'user' ? theme.primaryColor : undefined,
                        borderRadius: theme.borderRadius / 2,
                      }}
                    >
                      <p className="text-sm">{message.content}</p>
                      <span className="text-xs opacity-70 mt-1 block">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                ))}
                
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            <Separator />

            {/* Input */}
            <div className="p-4">
              <div className="flex items-center space-x-2">
                <div className="flex-1 relative">
                  <Input
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder={behavior.placeholder}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    className="pr-20"
                  />
                  
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                    {features.fileUpload && (
                      <Button size="sm" variant="ghost" className="p-1">
                        <Paperclip className="w-4 h-4" />
                      </Button>
                    )}
                    
                    {features.voiceInput && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className={`p-1 ${isRecording ? 'text-red-500' : ''}`}
                        onMouseDown={startVoiceRecording}
                        onMouseUp={stopVoiceRecording}
                        onMouseLeave={stopVoiceRecording}
                      >
                        {isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                      </Button>
                    )}
                  </div>
                </div>
                
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim()}
                  style={{ backgroundColor: theme.primaryColor }}
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </>
        )}
      </Card>
    </div>
  );
};