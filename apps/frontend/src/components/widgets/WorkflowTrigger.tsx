'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Settings, 
  Zap,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
} from 'lucide-react';
import { WidgetSDK } from '@/lib/widget-sdk';
import { WidgetConfig } from '@/types/widget.types';

interface WorkflowTriggerProps {
  config: WidgetConfig;
  workflowId: string;
  workflowName: string;
  description?: string;
  buttonText?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  onTrigger?: (workflowId: string) => void;
  onComplete?: (result: any) => void;
  onError?: (error: any) => void;
}

export const WorkflowTrigger: React.FC<WorkflowTriggerProps> = ({
  config,
  workflowId,
  workflowName,
  description,
  buttonText = 'Start Workflow',
  variant = 'default',
  size = 'default',
  className = '',
  onTrigger,
  onComplete,
  onError,
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [status, setStatus] = useState<'idle' | 'running' | 'completed' | 'error'>('idle');
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const theme = config.settings.theme;

  const handleTrigger = async () => {
    if (isRunning) return;

    try {
      setIsRunning(true);
      setStatus('running');
      setError(null);
      
      onTrigger?.(workflowId);

      // Initialize SDK if needed
      const sdk = new WidgetSDK(config);
      await sdk.initialize();

      // Track workflow trigger event
      await sdk.trackEvent('workflow_triggered', {
        workflowId,
        workflowName,
        timestamp: new Date().toISOString(),
      });

      // Simulate workflow execution (in production, this would call your workflow service)
      const workflowResult = await executeWorkflow(workflowId);
      
      setResult(workflowResult);
      setStatus('completed');
      onComplete?.(workflowResult);

      // Track completion
      await sdk.trackEvent('workflow_completed', {
        workflowId,
        workflowName,
        result: workflowResult,
        duration: workflowResult.duration,
      });

    } catch (error: any) {
      setError(error.message);
      setStatus('error');
      onError?.(error);
    } finally {
      setIsRunning(false);
    }
  };

  const executeWorkflow = async (workflowId: string): Promise<any> => {
    // Simulate API call to workflow service
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Simulate random success/failure
        if (Math.random() > 0.1) {
          resolve({
            workflowId,
            status: 'completed',
            duration: Math.floor(Math.random() * 5000) + 1000,
            steps: [
              { name: 'Initialize', status: 'completed', duration: 500 },
              { name: 'Process Data', status: 'completed', duration: 1200 },
              { name: 'Generate Output', status: 'completed', duration: 800 },
              { name: 'Finalize', status: 'completed', duration: 300 },
            ],
            output: {
              message: 'Workflow completed successfully',
              data: { processed: true, timestamp: new Date().toISOString() },
            },
          });
        } else {
          reject(new Error('Workflow execution failed'));
        }
      }, Math.random() * 3000 + 1000);
    });
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'running':
        return <Loader2 className="w-4 h-4 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Play className="w-4 h-4" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'running':
        return 'Running...';
      case 'completed':
        return 'Completed';
      case 'error':
        return 'Failed';
      default:
        return buttonText;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'running':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return theme.primaryColor;
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Workflow Info */}
      <div className="space-y-1">
        <div className="flex items-center space-x-2">
          <Zap className="w-5 h-5 text-gray-600" />
          <h3 className="font-medium text-gray-900 dark:text-gray-100">{workflowName}</h3>
          <Badge variant="outline" className="text-xs">
            {workflowId}
          </Badge>
        </div>
        
        {description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 ml-7">
            {description}
          </p>
        )}
      </div>

      {/* Trigger Button */}
      <Button
        variant={variant}
        size={size}
        onClick={handleTrigger}
        disabled={isRunning}
        className="w-full"
        style={{
          backgroundColor: status === 'idle' ? theme.primaryColor : undefined,
        }}
      >
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span>{getStatusText()}</span>
        </div>
      </Button>

      {/* Status Display */}
      {status !== 'idle' && (
        <div className="space-y-2">
          {/* Status Bar */}
          <div className="flex items-center space-x-2 text-sm">
            <div 
              className={`w-2 h-2 rounded-full ${
                status === 'running' ? 'animate-pulse' : ''
              }`}
              style={{ backgroundColor: getStatusColor() }}
            />
            <span className="text-gray-600 dark:text-gray-400">
              Status: {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
          </div>

          {/* Result Display */}
          {result && status === 'completed' && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium text-green-800 dark:text-green-200">
                  Workflow Completed
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Clock className="w-3 h-3 text-green-600" />
                  <span className="text-green-700 dark:text-green-300">
                    Duration: {result.duration}ms
                  </span>
                </div>
                
                {result.steps && (
                  <div className="space-y-1">
                    <span className="text-green-700 dark:text-green-300 font-medium">Steps:</span>
                    {result.steps.map((step: any, index: number) => (
                      <div key={index} className="flex items-center space-x-2 ml-4">
                        <CheckCircle className="w-3 h-3 text-green-500" />
                        <span className="text-green-600 dark:text-green-400">
                          {step.name} ({step.duration}ms)
                        </span>
                      </div>
                    ))}
                  </div>
                )}
                
                {result.output?.message && (
                  <p className="text-green-700 dark:text-green-300 mt-2">
                    {result.output.message}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && status === 'error' && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-4 h-4 text-red-500" />
                <span className="text-sm font-medium text-red-800 dark:text-red-200">
                  Workflow Failed
                </span>
              </div>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                {error}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};