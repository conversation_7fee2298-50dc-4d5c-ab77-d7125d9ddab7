import { WidgetConfig, WidgetSession, Message, AnalyticsEvent, VoiceInputResult, ChatResponse } from '@/types/widget.types';

export class WidgetSDK {
  private config: WidgetConfig;
  private session: WidgetSession | null = null;
  private eventListeners: Map<string, Function[]> = new Map();
  private websocket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(config: WidgetConfig) {
    this.config = config;
    this.validateDomain();
  }

  private validateDomain(): void {
    const currentDomain = window.location.hostname;
    const allowedDomains = this.config.settings.security.allowedDomains;

    if (allowedDomains.length > 0) {
      const isAllowed = allowedDomains.some(domain => {
        if (domain.startsWith('*.')) {
          const baseDomain = domain.substring(2);
          return currentDomain.endsWith(baseDomain);
        }
        return currentDomain === domain;
      });

      if (!isAllowed) {
        throw new Error(`Domain ${currentDomain} is not allowed for this widget`);
      }
    }
  }

  async initialize(): Promise<WidgetSession> {
    try {
      const response = await fetch(`${this.config.apiUrl}/widgets/${this.config.widgetId}/session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.config.apiKey,
        },
        body: JSON.stringify({
          metadata: {
            userAgent: navigator.userAgent,
            referrer: document.referrer,
            language: navigator.language,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            screenResolution: `${screen.width}x${screen.height}`,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to initialize session: ${response.statusText}`);
      }

      this.session = await response.json();
      
      // Initialize WebSocket connection for real-time updates
      await this.initializeWebSocket();
      
      // Track widget loaded event
      await this.trackEvent('widget_loaded', {
        timestamp: new Date().toISOString(),
      });

      this.emit('initialized', this.session);
      return this.session;
    } catch (error) {
      this.emit('error', { type: 'INITIALIZATION_ERROR', message: error.message });
      throw error;
    }
  }

  private async initializeWebSocket(): Promise<void> {
    if (!this.session) return;

    const wsUrl = this.config.apiUrl.replace('http', 'ws') + '/ws';
    this.websocket = new WebSocket(wsUrl);

    this.websocket.onopen = () => {
      this.reconnectAttempts = 0;
      this.emit('connected');
      
      // Subscribe to widget channels
      this.websocket?.send(JSON.stringify({
        type: 'subscribe',
        channels: [
          `widget.messages.${this.config.widgetId}`,
          `widget.config.${this.config.widgetId}`,
          `widget.analytics.${this.config.widgetId}`,
        ],
        token: this.session?.token,
      }));
    };

    this.websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleWebSocketMessage(data);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.websocket.onclose = () => {
      this.emit('disconnected');
      this.attemptReconnect();
    };

    this.websocket.onerror = (error) => {
      this.emit('error', { type: 'WEBSOCKET_ERROR', message: 'WebSocket connection error' });
    };
  }

  private handleWebSocketMessage(data: any): void {
    switch (data.type) {
      case 'widget.message.received':
        this.emit('messageReceived', data.payload);
        break;
      case 'widget.config.updated':
        this.emit('configUpdated', data.payload);
        break;
      case 'widget.analytics.event':
        this.emit('analyticsEvent', data.payload);
        break;
      default:
        this.emit('message', data);
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('error', { type: 'CONNECTION_FAILED', message: 'Max reconnection attempts reached' });
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff

    setTimeout(() => {
      this.initializeWebSocket();
    }, delay);
  }

  async sendMessage(message: string, options?: {
    language?: string;
    metadata?: any;
  }): Promise<ChatResponse> {
    if (!this.session) {
      throw new Error('Widget not initialized');
    }

    try {
      const response = await fetch(`${this.config.apiUrl}/widgets/${this.config.widgetId}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.session.token}`,
        },
        body: JSON.stringify({
          message,
          sessionId: this.session.sessionId,
          language: options?.language || 'en',
          metadata: options?.metadata,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`);
      }

      const result = await response.json();
      this.emit('messageSent', { message, response: result });
      return result;
    } catch (error) {
      this.emit('error', { type: 'MESSAGE_SEND_ERROR', message: error.message });
      throw error;
    }
  }

  async processVoiceInput(audioBlob: Blob, options?: {
    language?: string;
    metadata?: any;
  }): Promise<VoiceInputResult> {
    if (!this.session) {
      throw new Error('Widget not initialized');
    }

    if (!this.config.settings.features.voiceInput) {
      throw new Error('Voice input is not enabled for this widget');
    }

    try {
      const formData = new FormData();
      formData.append('audio', audioBlob);
      formData.append('language', options?.language || 'en');
      formData.append('sessionId', this.session.sessionId);
      
      if (options?.metadata) {
        formData.append('metadata', JSON.stringify(options.metadata));
      }

      const response = await fetch(`${this.config.apiUrl}/widgets/${this.config.widgetId}/voice`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.session.token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to process voice input: ${response.statusText}`);
      }

      const result = await response.json();
      this.emit('voiceProcessed', result);
      return result;
    } catch (error) {
      this.emit('error', { type: 'VOICE_PROCESSING_ERROR', message: error.message });
      throw error;
    }
  }

  async trackEvent(eventType: string, eventData: Record<string, any>): Promise<void> {
    if (!this.session) return;

    try {
      await fetch(`${this.config.apiUrl}/widgets/${this.config.widgetId}/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.session.token}`,
        },
        body: JSON.stringify({
          widgetId: this.config.widgetId,
          eventType,
          eventData,
          sessionId: this.session.sessionId,
          metadata: {
            timestamp: new Date().toISOString(),
            url: window.location.href,
          },
        }),
      });
    } catch (error) {
      console.error('Failed to track event:', error);
    }
  }

  async updateLanguage(language: string): Promise<void> {
    await this.trackEvent('language_changed', { 
      from: this.getCurrentLanguage(),
      to: language,
    });
    
    this.emit('languageChanged', { language });
  }

  getCurrentLanguage(): string {
    return navigator.language.split('-')[0];
  }

  async destroy(): Promise<void> {
    if (this.session) {
      try {
        await fetch(`${this.config.apiUrl}/widgets/${this.config.widgetId}/session/${this.session.sessionId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.session.token}`,
          },
        });
      } catch (error) {
        console.error('Failed to end session:', error);
      }
    }

    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }

    this.session = null;
    this.eventListeners.clear();
    this.emit('destroyed');
  }

  // Event system
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback?: Function): void {
    if (!this.eventListeners.has(event)) return;

    if (callback) {
      const listeners = this.eventListeners.get(event)!;
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    } else {
      this.eventListeners.delete(event);
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  // Utility methods
  isInitialized(): boolean {
    return this.session !== null;
  }

  getSession(): WidgetSession | null {
    return this.session;
  }

  getConfig(): WidgetConfig {
    return this.config;
  }

  updateConfig(newConfig: Partial<WidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  // Rate limiting check
  private rateLimitCheck = new Map<string, number[]>();

  private checkRateLimit(action: string): boolean {
    const now = Date.now();
    const limit = this.config.settings.security.rateLimitPerMinute;
    const window = 60 * 1000; // 1 minute

    if (!this.rateLimitCheck.has(action)) {
      this.rateLimitCheck.set(action, []);
    }

    const timestamps = this.rateLimitCheck.get(action)!;
    
    // Remove old timestamps
    while (timestamps.length > 0 && timestamps[0] < now - window) {
      timestamps.shift();
    }

    if (timestamps.length >= limit) {
      return false;
    }

    timestamps.push(now);
    return true;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    try {
      const response = await fetch(`${this.config.apiUrl}/widgets/${this.config.widgetId}/health`);
      return await response.json();
    } catch (error) {
      throw new Error(`Health check failed: ${error.message}`);
    }
  }
}