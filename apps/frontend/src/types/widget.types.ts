export interface WidgetConfig {
  widgetId: string;
  apiKey: string;
  apiUrl: string;
  settings: {
    theme: {
      primaryColor: string;
      position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
      size: 'small' | 'medium' | 'large';
      borderRadius: number;
      shadow: boolean;
      animation: boolean;
    };
    features: {
      voiceInput: boolean;
      fileUpload: boolean;
      multiLanguage: boolean;
      darkMode: boolean;
      minimizable: boolean;
      draggable: boolean;
      resizable: boolean;
    };
    behavior: {
      autoOpen: boolean;
      greeting?: string;
      placeholder: string;
      maxMessages: number;
      typingIndicator: boolean;
      readReceipts: boolean;
    };
    security: {
      allowedDomains: string[];
      rateLimitPerMinute: number;
      sessionTimeout: number;
      requireAuth: boolean;
    };
  };
}

export interface WidgetSession {
  sessionId: string;
  token: string;
  widgetId: string;
}

export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
  type: 'text' | 'voice' | 'file';
  metadata?: any;
}

export interface AnalyticsEvent {
  eventType: string;
  eventData: Record<string, any>;
  sessionId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface VoiceInputResult {
  transcript: string;
  confidence: number;
  language: string;
}

export interface ChatResponse {
  response: string;
  metadata: {
    timestamp: string;
    language: string;
    processingTime: number;
  };
}

export interface WidgetTheme {
  id: string;
  name: string;
  config: {
    colors: {
      primary: string;
      secondary: string;
      accent: string;
      background: string;
      surface: string;
      text: string;
      textSecondary: string;
      border: string;
      error: string;
      success: string;
      warning: string;
    };
    typography: {
      fontFamily: string;
      fontSize: {
        xs: string;
        sm: string;
        base: string;
        lg: string;
        xl: string;
      };
      fontWeight: {
        normal: number;
        medium: number;
        semibold: number;
        bold: number;
      };
    };
    spacing: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    borderRadius: {
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    shadows: {
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
  };
  isDefault: boolean;
}

export interface WidgetError {
  type: string;
  message: string;
  details?: Record<string, any>;
  widgetId?: string;
  sessionId?: string;
  timestamp: Date;
}

export interface AnalyticsSummary {
  period: 'day' | 'week' | 'month';
  totalEvents: number;
  eventBreakdown: Array<{
    eventType: string;
    count: number;
    percentage: string;
  }>;
}

export interface RealTimeMetrics {
  [eventType: string]: {
    hourly: Record<number, number>;
    daily: number;
  };
}