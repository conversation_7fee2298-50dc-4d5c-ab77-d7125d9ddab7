import { FloatingAssistant } from "@/components/widgets/FloatingAssistant";

export default function FloatingAssistantDemo() {
  return (
    <div className="bg-gray-50 min-h-screen p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          UAUI Frontend SDK - Floating Assistant Demo
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Features</h2>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>✅ Real-time WebSocket communication via APIX</li>
              <li>✅ Voice input with speech-to-text</li>
              <li>✅ Text-to-speech responses</li>
              <li>✅ Multi-language support</li>
              <li>✅ Draggable and resizable</li>
              <li>✅ File upload support</li>
              <li>✅ Customizable themes</li>
              <li>✅ Session persistence</li>
              <li>✅ Analytics tracking</li>
              <li>✅ RBAC & security</li>
            </ul>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Configuration</h2>
            <div className="text-sm text-gray-600 space-y-2">
              <div><strong>Widget ID:</strong> demo-widget-001</div>
              <div><strong>Position:</strong> Bottom Right</div>
              <div><strong>Theme:</strong> Blue (#3b82f6)</div>
              <div><strong>Size:</strong> Medium (400x600)</div>
              <div><strong>Features:</strong> All enabled</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Integration Example</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
{`<FloatingAssistant
  widgetId="demo-widget-001"
  apiUrl="/api"
  settings={{
    theme: {
      primaryColor: "#3b82f6",
      position: "bottom-right",
      size: "medium"
    },
    features: {
      voiceInput: true,
      fileUpload: true,
      multiLanguage: true,
      draggable: true
    },
    behavior: {
      autoOpen: false,
      greeting: "Hello! How can I help you today?",
      placeholder: "Type your message..."
    }
  }}
  onMessage={(message) => console.log('New message:', message)}
  onStateChange={(state) => console.log('State changed:', state)}
/>`}
          </pre>
        </div>
      </div>
      
      {/* The actual floating assistant widget */}
      <FloatingAssistant
        widgetId="demo-widget-001"
        apiUrl="/api"
        settings={{
          theme: {
            primaryColor: "#3b82f6",
            position: "bottom-right",
            size: "medium",
            borderRadius: 12,
            shadow: true,
            animation: true
          },
          features: {
            voiceInput: true,
            fileUpload: true,
            multiLanguage: true,
            darkMode: false,
            minimizable: true,
            draggable: true,
            resizable: false
          },
          behavior: {
            autoOpen: false,
            greeting: "Hello! I'm your AI assistant powered by SynapseAI. How can I help you today?",
            placeholder: "Type your message...",
            maxMessages: 100,
            typingIndicator: true,
            readReceipts: true
          }
        }}
        onMessage={(message) => {
          console.log('New message:', message);
        }}
        onStateChange={(state) => {
          console.log('Widget state changed:', state);
        }}
      />
    </div>
  );
}