'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bot, 
  MessageCircle, 
  Zap, 
  Settings,
  Code,
  Eye,
  Copy,
  Check,
} from 'lucide-react';
import { SynapseWidget } from '@/components/widgets/SynapseWidget';
import { ChatPanel } from '@/components/widgets/ChatPanel';
import { WorkflowTrigger } from '@/components/widgets/WorkflowTrigger';
import { AgentEmbed } from '@/components/widgets/AgentEmbed';
import { WidgetConfig } from '@/types/widget.types';

export default function UAUIDemo() {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  // Sample widget configuration
  const sampleConfig: WidgetConfig = {
    widgetId: 'demo-widget-001',
    api<PERSON>ey: 'wk_demo_key_12345',
    apiUrl: '/api',
    settings: {
      theme: {
        primaryColor: '#3b82f6',
        position: 'bottom-right',
        size: 'medium',
        borderRadius: 12,
        shadow: true,
        animation: true,
      },
      features: {
        voiceInput: true,
        fileUpload: true,
        multiLanguage: true,
        darkMode: false,
        minimizable: true,
        draggable: true,
        resizable: false,
      },
      behavior: {
        autoOpen: false,
        greeting: 'Hello! I\'m your AI assistant. How can I help you today?',
        placeholder: 'Type your message...',
        maxMessages: 100,
        typingIndicator: true,
        readReceipts: true,
      },
      security: {
        allowedDomains: ['localhost:3000', 'demo.synapseai.com'],
        rateLimitPerMinute: 60,
        sessionTimeout: 3600,
        requireAuth: false,
      },
    },
  };

  const copyToClipboard = (code: string, id: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(id);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  const codeExamples = {
    synapseWidget: `import { SynapseWidget } from '@/components/widgets/SynapseWidget';

const config = {
  widgetId: 'your-widget-id',
  apiKey: 'your-api-key',
  apiUrl: 'https://api.synapseai.com',
  settings: {
    theme: {
      primaryColor: '#3b82f6',
      position: 'bottom-right',
      size: 'medium',
    },
    features: {
      voiceInput: true,
      multiLanguage: true,
      draggable: true,
    },
    behavior: {
      greeting: 'Hello! How can I help?',
      autoOpen: false,
    },
  },
};

<SynapseWidget 
  config={config}
  onMessage={(message) => console.log(message)}
  onStateChange={(state) => console.log(state)}
/>`,

    chatPanel: `import { ChatPanel } from '@/components/widgets/ChatPanel';

<ChatPanel 
  config={config}
  height={500}
  onMessage={(message) => console.log(message)}
  className="w-full max-w-md"
/>`,

    workflowTrigger: `import { WorkflowTrigger } from '@/components/widgets/WorkflowTrigger';

<WorkflowTrigger
  config={config}
  workflowId="workflow-123"
  workflowName="Data Processing"
  description="Process and analyze customer data"
  buttonText="Start Processing"
  onComplete={(result) => console.log(result)}
/>`,

    agentEmbed: `import { AgentEmbed } from '@/components/widgets/AgentEmbed';

<AgentEmbed
  config={config}
  agentId="agent-456"
  agentName="Customer Support Agent"
  agentDescription="Specialized in customer inquiries"
  capabilities={['Chat Support', 'Order Tracking', 'FAQ']}
  height={400}
  onInteraction={(type, data) => console.log(type, data)}
/>`,
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2">
            <Bot className="w-8 h-8 text-blue-600" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              UAUI Frontend SDK
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Universal Agent UI - A complete React-based SDK for embedding SynapseAI's 
            multi-agent orchestration platform into any web application.
          </p>
          <div className="flex items-center justify-center space-x-2">
            <Badge variant="secondary">Production Ready</Badge>
            <Badge variant="secondary">Real-time Sync</Badge>
            <Badge variant="secondary">Multi-language</Badge>
            <Badge variant="secondary">Voice Enabled</Badge>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="p-6 text-center">
            <MessageCircle className="w-8 h-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Floating Assistant</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Draggable AI chat widget with voice and multilingual support
            </p>
          </Card>

          <Card className="p-6 text-center">
            <Bot className="w-8 h-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Agent Embed</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Embeddable agent interface for third-party integration
            </p>
          </Card>

          <Card className="p-6 text-center">
            <Zap className="w-8 h-8 text-yellow-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Workflow Triggers</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Configurable buttons to launch workflows and agent tasks
            </p>
          </Card>

          <Card className="p-6 text-center">
            <Settings className="w-8 h-8 text-purple-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Real-time Sync</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              APIX protocol for event-driven communication
            </p>
          </Card>
        </div>

        {/* Demo Tabs */}
        <Tabs defaultValue="floating-assistant" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="floating-assistant">Floating Assistant</TabsTrigger>
            <TabsTrigger value="chat-panel">Chat Panel</TabsTrigger>
            <TabsTrigger value="workflow-trigger">Workflow Trigger</TabsTrigger>
            <TabsTrigger value="agent-embed">Agent Embed</TabsTrigger>
          </TabsList>

          {/* Floating Assistant Demo */}
          <TabsContent value="floating-assistant" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold">Floating Assistant Widget</h2>
                  <Badge variant="outline">Interactive Demo</Badge>
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  A draggable, minimizable AI chat widget that can be embedded anywhere on your site. 
                  Features voice input, multi-language support, and real-time messaging.
                </p>
                
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    Try it out!
                  </h3>
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    Look for the floating chat button in the bottom-right corner of this page. 
                    Click it to start a conversation with the AI assistant.
                  </p>
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold">Key Features:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                    <li>Draggable and resizable interface</li>
                    <li>Voice input with speech-to-text</li>
                    <li>Multi-language support (EN, ES, FR, DE, ZH)</li>
                    <li>Real-time message synchronization</li>
                    <li>Customizable themes and positioning</li>
                    <li>Connection status indicators</li>
                  </ul>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">Implementation Code</h3>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(codeExamples.synapseWidget, 'synapse')}
                  >
                    {copiedCode === 'synapse' ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg text-xs overflow-x-auto">
                  <code>{codeExamples.synapseWidget}</code>
                </pre>
              </div>
            </div>
          </TabsContent>

          {/* Chat Panel Demo */}
          <TabsContent value="chat-panel" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold">Chat Panel Component</h2>
                  <Badge variant="outline">Live Demo</Badge>
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  An inline chat component that can be embedded directly into your application's UI. 
                  Perfect for dedicated chat pages or sidebar integrations.
                </p>

                <ChatPanel 
                  config={sampleConfig}
                  height={400}
                  onMessage={(message) => console.log('Chat message:', message)}
                  onError={(error) => console.error('Chat error:', error)}
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">Implementation Code</h3>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(codeExamples.chatPanel, 'chat')}
                  >
                    {copiedCode === 'chat' ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg text-xs overflow-x-auto">
                  <code>{codeExamples.chatPanel}</code>
                </pre>

                <div className="space-y-2">
                  <h3 className="font-semibold">Features:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                    <li>Responsive design for mobile and desktop</li>
                    <li>Real-time typing indicators</li>
                    <li>Voice input support</li>
                    <li>File upload capabilities</li>
                    <li>Connection status monitoring</li>
                    <li>Error handling and recovery</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Workflow Trigger Demo */}
          <TabsContent value="workflow-trigger" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold">Workflow Trigger</h2>
                  <Badge variant="outline">Interactive</Badge>
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  Configurable buttons that can trigger workflows or agent tasks. 
                  Includes real-time status updates and result display.
                </p>

                <div className="space-y-4">
                  <WorkflowTrigger
                    config={sampleConfig}
                    workflowId="demo-workflow-001"
                    workflowName="Customer Data Analysis"
                    description="Analyze customer behavior patterns and generate insights"
                    buttonText="Start Analysis"
                    onTrigger={(id) => console.log('Workflow triggered:', id)}
                    onComplete={(result) => console.log('Workflow completed:', result)}
                    onError={(error) => console.error('Workflow error:', error)}
                  />

                  <WorkflowTrigger
                    config={sampleConfig}
                    workflowId="demo-workflow-002"
                    workflowName="Email Campaign Generator"
                    description="Generate personalized email campaigns based on user segments"
                    buttonText="Generate Campaign"
                    variant="outline"
                    onTrigger={(id) => console.log('Workflow triggered:', id)}
                    onComplete={(result) => console.log('Workflow completed:', result)}
                    onError={(error) => console.error('Workflow error:', error)}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">Implementation Code</h3>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(codeExamples.workflowTrigger, 'workflow')}
                  >
                    {copiedCode === 'workflow' ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg text-xs overflow-x-auto">
                  <code>{codeExamples.workflowTrigger}</code>
                </pre>

                <div className="space-y-2">
                  <h3 className="font-semibold">Capabilities:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                    <li>Real-time workflow execution status</li>
                    <li>Step-by-step progress tracking</li>
                    <li>Result display with detailed metrics</li>
                    <li>Error handling and retry mechanisms</li>
                    <li>Customizable button styles and sizes</li>
                    <li>Analytics tracking for workflow usage</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Agent Embed Demo */}
          <TabsContent value="agent-embed" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold">Agent Embed</h2>
                  <Badge variant="outline">Live Agent</Badge>
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  Embeddable agent interface perfect for showcasing AI agents on websites, 
                  documentation, or third-party applications.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <AgentEmbed
                    config={sampleConfig}
                    agentId="agent-customer-support"
                    agentName="Customer Support Agent"
                    agentDescription="Specialized in handling customer inquiries, order tracking, and general support questions."
                    capabilities={['Order Tracking', 'Returns & Refunds', 'Product Info', 'Technical Support']}
                    height={350}
                    onInteraction={(type, data) => console.log('Agent interaction:', type, data)}
                    onError={(error) => console.error('Agent error:', error)}
                  />

                  <AgentEmbed
                    config={sampleConfig}
                    agentId="agent-sales-assistant"
                    agentName="Sales Assistant"
                    agentDescription="Helps with product recommendations, pricing information, and sales inquiries."
                    capabilities={['Product Recommendations', 'Pricing', 'Demos', 'Lead Qualification']}
                    height={350}
                    showCapabilities={true}
                    onInteraction={(type, data) => console.log('Agent interaction:', type, data)}
                    onError={(error) => console.error('Agent error:', error)}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">Implementation Code</h3>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(codeExamples.agentEmbed, 'agent')}
                  >
                    {copiedCode === 'agent' ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg text-xs overflow-x-auto">
                  <code>{codeExamples.agentEmbed}</code>
                </pre>

                <div className="space-y-2">
                  <h3 className="font-semibold">Features:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                    <li>Real-time agent status monitoring</li>
                    <li>Capability showcase and descriptions</li>
                    <li>Interactive chat and workflow triggers</li>
                    <li>Performance metrics display</li>
                    <li>Customizable appearance and branding</li>
                    <li>Integration with external systems</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Technical Specifications */}
        <Card className="p-6">
          <h2 className="text-2xl font-bold mb-4">Technical Specifications</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h3 className="font-semibold mb-2">Security & Authentication</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• JWT-based session management</li>
                <li>• API key authentication</li>
                <li>• Domain allowlist validation</li>
                <li>• Rate limiting per widget</li>
                <li>• CSP enforcement</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">Real-time Communication</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• APIX protocol integration</li>
                <li>• WebSocket connections</li>
                <li>• Event-driven architecture</li>
                <li>• Automatic reconnection</li>
                <li>• Message queuing</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">Analytics & Monitoring</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Real-time usage metrics</li>
                <li>• Event tracking</li>
                <li>• Performance monitoring</li>
                <li>• Error reporting</li>
                <li>• Custom analytics hooks</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* Footer */}
        <div className="text-center text-gray-600 dark:text-gray-400">
          <p>
            UAUI Frontend SDK - Production-ready components for SynapseAI integration
          </p>
          <p className="text-sm mt-2">
            Built with React, TypeScript, and Tailwind CSS
          </p>
        </div>
      </div>

      {/* Floating Assistant Widget */}
      <SynapseWidget 
        config={sampleConfig}
        onMessage={(message) => console.log('Floating widget message:', message)}
        onStateChange={(state) => console.log('Widget state:', state)}
        onError={(error) => console.error('Widget error:', error)}
      />
    </div>
  );
}