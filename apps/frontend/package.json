{"name": "@synapse/frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@synapse/ui": "workspace:*", "@synapse/shared": "workspace:*", "@synapse/config": "workspace:*", "@synapse/services": "workspace:*", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "embla-carousel-react": "^8.5.2", "framer-motion": "^10.16.16", "i18next": "^23.7.16", "react-i18next": "^13.5.0", "jotai": "^2.6.2", "lucide-react": "^0.468.0", "next": "14.2.23", "next-themes": "^0.2.1", "react": "^18", "react-dom": "^18", "react-day-picker": "^9.5.1", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "swr": "^2.2.4", "tempo-devtools": "^2.0.109", "vaul": "^1.1.2", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@playwright/test": "^1.40.1", "autoprefixer": "10.4.20", "eslint": "^8.56.0", "eslint-config-next": "14.2.23", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.6", "postcss": "^8", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5"}}