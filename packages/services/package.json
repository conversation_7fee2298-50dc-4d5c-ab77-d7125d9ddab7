{"name": "@synapse/services", "version": "0.1.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint \"src/**/*.ts\"", "type-check": "tsc --noEmit"}, "dependencies": {"@synapse/shared": "workspace:*", "socket.io-client": "^4.7.4", "swr": "^2.2.4", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "eslint": "^8.56.0", "tsup": "^8.0.1", "typescript": "^5"}}