{"name": "@synapse/ui", "version": "0.1.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint \"src/**/*.{ts,tsx}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "embla-carousel-react": "^8.5.2", "framer-motion": "^10.16.16", "lucide-react": "^0.468.0", "react-day-picker": "^9.5.1", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "tailwind-merge": "^2", "tailwindcss-animate": "^1", "vaul": "^1.1.2"}, "devDependencies": {"@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8.56.0", "react": "^18", "react-dom": "^18", "tailwindcss": "^3", "tsup": "^8.0.1", "typescript": "^5"}, "peerDependencies": {"react": "^18", "react-dom": "^18"}}