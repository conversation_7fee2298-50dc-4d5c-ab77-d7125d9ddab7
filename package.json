{"name": "synapse-monorepo", "private": true, "workspaces": ["apps/*", "packages/*"], "packageManager": "pnpm@8.15.0", "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "type-check": "turbo run type-check", "test": "turbo run test", "test:e2e": "turbo run test:e2e", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "prepare": "husky install", "backend:dev": "pnpm --filter @synapse/backend run start:dev", "frontend:dev": "pnpm --filter @synapse/frontend run dev", "prisma:generate": "pnpm --filter @synapse/backend run prisma:generate", "prisma:migrate": "pnpm --filter @synapse/backend run prisma:migrate", "prisma:studio": "pnpm --filter @synapse/backend run prisma:studio"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/node": "^20.10.5", "eslint": "^8.56.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "turbo": "^1.11.2", "typescript": "^5.3.3", "tempo-devtools": "^2.0.109"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}